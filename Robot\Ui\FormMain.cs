using AiHelper;
using Flurl.Http;
using Newtonsoft.Json;
using Robot.ChatPlatform;
using Robot.Config;
using Robot.Constants;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;
using Robot.Services;
using Sunny.UI;

namespace Robot.Ui;

public partial class FormMain : Form
{
    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    public FormMain()
    {
        InitializeComponent();

        // 同步上下文
        CommonHelper.Context = SynchronizationContext.Current!;

        // 开启服务标记
        CommonHelper.ServiceParamDic.TryAdd(CommonHelper.IsStartService, false);
    }

    #endregion

    #region 载入主窗体

    /// <summary>
    /// 载入主窗体
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void FormMain_Load(object sender, EventArgs e)
    {
        try
        {
            // 最小化到托盘
            // WindowState = FormWindowState.Minimized;
            // this.Hide();

            // 启动日志
            await DbHelper.AddLogAsync(EnumLogType.机器人, "Robot启动", "Robot启动");
            if (CommonHelper.Lottery == EnumLottery.台湾宾果)
            {
                await DbHelper.AddLogAsync(EnumLogType.平台, "当前游戏", "台湾宾果");
            }
            else if (CommonHelper.Lottery == EnumLottery.一六八飞艇)
            {
                await DbHelper.AddLogAsync(EnumLogType.平台, "当前游戏", "168飞艇");
            }

            // 初始化数据库
            DbHelper.InitTableAsync();

            // 反序列化
            string jsonString = await File.ReadAllTextAsync(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "Odds.json"));
            CommonHelper.LotteryGames = JsonConvert.DeserializeObject<LotteryGameModel>(jsonString)!;

            // 启动时清空开奖信息
            await DbHelper.FSql.Delete<KaiJiang>()
                .Where("1=1")
                .ExecuteAffrowsAsync();

            // 启动时创建期号时间信息
            await IssueTimeHelper.CreateIssueTimeAsync(CommonHelper.Cts.Token);

            // 初始化声音服务
            SoundHelper.InitSoundService();

            // 初始化UI
            await InitUi();

            // 启动监听
            if (CommonHelper.ChatApp != EnumChatApp.微信391125)
            {
                _ = Task.Run(HttpListenerHelper.ListeningAsync);
                _ = Task.Run(HttpListenerHelper.ProcessMessageQueueAsync);
            }

            // 启动服务
            await Task.WhenAll(
                IssueTimeHelper.PreIssueTimeAsync(CommonHelper.Cts.Token),
                ServiceAsync(CommonHelper.Cts.Token)
            );
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "Robot启动异常", ex.ToString());
        }
    }

    #endregion

    #region 服务主线程

    /// <summary>
    /// 服务主线程
    /// </summary>
    private async Task ServiceAsync(CancellationToken token)
    {
        long whileCount = 0;
        while (!token.IsCancellationRequested)
        {
            await Task.Delay(1000, token);
            whileCount++;

            try
            {
                // 判断获取消息列表,WeChat391125需要独立读取消息,把读取消息放在这里，作用是先将旧的消息读取完毕,(该接口基于网友大表舅的Hook！)
                if (CommonHelper.ChatApp == EnumChatApp.微信391125)
                {
                    await ChatHelper.GetMsgList();
                }

                // 每1秒刷新基础信息
                if (whileCount % UiConstants.RefreshIntervalBasic == 0)
                {
                    await Task.Run(ShowInfoAsync, token);
                    await Task.Run(ShowAddMoneyOrderAsync, token);
                    await Task.Run(ShowSubMoneyOrderAsync, token);
                    await Task.Run(ShowPlatformLogAsync, token);
                }

                // 每2秒刷新一次订单
                if (whileCount % UiConstants.RefreshIntervalOrder == 0)
                {
                    await Task.Run(ShowBetOrderAsync, token);
                }

                // 每3秒刷新一次会员信息
                if (whileCount % UiConstants.RefreshIntervalMemberInfo == 0)
                {
                    await Task.Run(ShowMemberInfoAsync, token);
                }

                #region Robot 相关操作

                // 判断获取Robot账号信息
                if (string.IsNullOrEmpty(RobotHelper.RobotInfo.Account))
                {
                    // 微信360018版本使用千寻微信框架,通过获取微信列表信息获取Robot信息
                    if (CommonHelper.ChatApp == EnumChatApp.微信360018)
                    {
                        await WeChatHelper360018.GetWeChatList();
                    }
                    else
                    {
                        // 其他版本通过获取Robot信息获取Robot信息
                        await ChatHelper.GetRobotInfo();
                    }

                    continue;
                }

                // 判断获取Robot群列表
                if (RobotHelper.GroupDic.Count.Equals(0))
                {
                    await ChatHelper.GetGroupDic();
                    continue;
                }

                #endregion

                #region 平台相关操作

                // 把游戏种类传递给平台端,平台端根据游戏种类获取对应开奖信息
                try
                {
                    string url = $"{RobotSetting.Current.PlatformHost}/GameInfo/";
                    await url.WithTimeout(TimeSpan.FromSeconds(3))
                        .PostJsonAsync(new
                        {
                            CommonHelper.Lottery
                        }, cancellationToken: token);
                }
                catch (Exception ex)
                {
                    await DbHelper.AddLogAsync(EnumLogType.机器人, "传递游戏种类Error", ex.ToString());
                }

                // 获取平台最新额度信息
                try
                {
                    string url = $"{RobotSetting.Current.PlatformHost}/UserInfo/";
                    UserInfo? tmpUserInfo = await url.WithTimeout(TimeSpan.FromSeconds(3)).PostAsync(cancellationToken: token).ReceiveJson<UserInfo>();
                    if (tmpUserInfo != null)
                    {
                        if (CommonHelper.UserInfo != null && !CommonHelper.UserInfo.Balance.Equals(tmpUserInfo.Balance))
                        {
                            CommonHelper.UserInfo = tmpUserInfo;
                            await DbHelper.AddLogAsync(EnumLogType.平台, "额度变动", $"最新额度:{Ai.中括号左}{CommonHelper.UserInfo.Balance}{Ai.中括号右}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    await DbHelper.AddLogAsync(EnumLogType.机器人, "获取平台额度信息异常", ex.ToString());
                }

                // 处理开奖
                bool hasNewDraw = await DrawService.ProcessDrawInfo(token);
                if (hasNewDraw)
                {
                    // 结算答题数据：处理用户的投注订单，计算中奖情况并更新账户余额
                    await RobotHelper.结算答题数据();

                    // 结算飞单数据：处理系统的飞单汇总数据，计算盈亏情况
                    await Task.Delay(500, token);
                    await RobotHelper.结算飞单数据();

                    // 第九步：自动发送开奖数据图
                    // 根据系统配置决定是否自动向群聊发送开奖图片
                    if (CommonHelper.ServiceParamDic[CommonHelper.IsStartService] && UserSetting.Current.是否发送开奖图)
                    {
                        // 延迟500毫秒，确保图片生成完成
                        await Task.Delay(500, token);
                        await RobotHelper.发送开奖图Handler();
                    }

                    // 第十步：自动发送开奖路子图
                    // 根据系统配置决定是否自动向群聊发送路子图
                    if (CommonHelper.ServiceParamDic[CommonHelper.IsStartService] && UserSetting.Current.是否发送路子图)
                    {
                        // 延迟500毫秒，避免消息发送过于频繁
                        await Task.Delay(500, token);
                        await RobotHelper.发送路子图Handler();
                    }
                }

                #endregion

                // 处理假人自动上分
                if (UserSetting.Current.假人自动上分)
                {
                    await JiaRenAutoAddMoneyHandler();
                }

                // 判断开始状态,如果未开始则将未读消息标记为已读,并跳过
                if (!CommonHelper.ServiceParamDic[CommonHelper.IsStartService])
                {
                    // 把未读消息标记为已读
                    await DbHelper.FSql.Update<ReceiveMessage>()
                        .Set(a => a.IsRead, true)
                        .Set(a => a.PreTime, DateTime.Now)
                        .Where(a => a.IsRead == false)
                        .ExecuteAffrowsAsync(token);

                    // 跳到下一轮循环
                    continue;
                }

                // 提取未读消息
                List<ReceiveMessage> msgList = await DbHelper.FSql.Select<ReceiveMessage>()
                    .Where(a => a.IsRead == false)
                    .Where(a => a.ReceiveTime > CommonHelper.StartServiceTime)
                    .ToListAsync(token);

                // 记录处理消息条数
                if (msgList.Count > 0)
                {
                    await DbHelper.AddLogAsync(EnumLogType.机器人, "处理用户消息", $"共有{msgList.Count}条消息等待处理,{msgList[0].Id}-{Enumerable.Last(msgList).Id}");
                }

                // 遍历未读消息并处理
                foreach (ReceiveMessage msg in msgList)
                {
                    // 更新数据库
                    await DbHelper.FSql.Update<ReceiveMessage>()
                        .Set(a => a.IsRead, true)
                        .Set(a => a.PreTime, DateTime.Now)
                        .Where(a => a.Id == msg.Id)
                        .ExecuteAffrowsAsync(token);

                    // 处理消息
                    await RobotHelper.ReceiveMessageHandlerAsync(msg);
                }

                // 提取出当前期号
                string issueNow = IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue;

                // 判断开盘提醒
                if (!RobotHelper.OpenTipsList.Contains(issueNow)
                    && IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] <= UserSetting.Current.开盘时间
                    && IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] > UserSetting.Current.封盘时间)
                {
                    // 取出所有开奖数据
                    List<KaiJiang>? kjList = await DbHelper.FSql.Select<KaiJiang>().ToListAsync(token);

                    // 如果已经有开奖数据,则判断上一期开奖数据是否存在
                    if (kjList.Count > 0)
                    {
                        // 取出当前期的上一期期号信息
                        IssueTime lastIssue = await DbHelper.FSql.Select<IssueTime>()
                            .Where(a => a.Id.Equals(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Id - 1))
                            .ToOneAsync(token);

                        KaiJiang lastIssueDraw = await DbHelper.FSql.Select<KaiJiang>()
                            .Where(a => a.Issue == lastIssue.Issue)
                            .ToOneAsync(token);

                        // 若上一期开奖数据不存在,则进入下一轮循环, 也可以通过手动开盘接受答题
                        if (lastIssueDraw == null)
                        {
                            continue;
                        }
                    }

                    // 记录开盘提示
                    RobotHelper.OpenTipsList.Add(issueNow);
                    await RobotHelper.发送开盘提醒(issueNow);
                    await DbHelper.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "开始接受答题" + Ai.中括号右, string.Concat(Ai.中括号左, issueNow, Ai.中括号右, "期开始接受答题"));

                    // 发送开盘提示音
                    SoundHelper.MediaOpenDraw.Play();
                }

                // 判断封盘倒计时提醒
                if (RobotHelper.OpenTipsList.Contains(issueNow)
                    && !RobotHelper.CloseDownTipsList.Contains(issueNow)
                    && IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] <= UserSetting.Current.封盘时间 + 30)
                {
                    RobotHelper.CloseDownTipsList.Add(issueNow);
                    await DbHelper.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "封盘时间提醒" + Ai.中括号右, string.Concat(Ai.中括号左, issueNow, Ai.中括号右, "封卷时间还剩", (IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] - UserSetting.Current.封盘时间 + 1).ToString(), "秒"));
                    await RobotHelper.发送封盘倒计时提醒(issueNow);
                }

                // 判断封盘处理
                if (RobotHelper.OpenTipsList.Contains(issueNow)
                    && !RobotHelper.CloseTipsList.Contains(issueNow)
                    && IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] <= UserSetting.Current.封盘时间)
                {
                    // 停止答题
                    RobotHelper.CloseTipsList.Add(issueNow);
                    await DbHelper.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "停止答题提醒" + Ai.中括号右, string.Concat(Ai.中括号左, issueNow, Ai.中括号右, "停止答题", Ai.中括号左, issueNow, Ai.中括号右));

                    // 记录日志
                    await DbHelper.AddLogAsync(EnumLogType.平台, "停止答题", $"封盘设置:{Ai.中括号左}{UserSetting.Current.封盘时间}{Ai.中括号右},倒计时余:{Ai.中括号左}{IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery]}{Ai.中括号右}");

                    // 提取本期所有已受理的下注数据
                    List<BetOrder>? betOrderList = await DbHelper.FSql.Select<BetOrder>()
                        .Where(a => a.Issue == issueNow)
                        .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已受理)
                        .ToListAsync(token);

                    // 实时提取所有Member,根据用户真假状态变更注单状态
                    List<Member> memberList = await DbHelper.FSql.Select<Member>().ToListAsync(token);
                    foreach (BetOrder betOrder in betOrderList)
                    {
                        // 提取订单类型
                        betOrder.OrderType = memberList.First(a => a.Account == betOrder.Account).是否假人 ? EnumOrderType.假人订单 : EnumOrderType.真人订单;

                        // 更新数据库
                        await DbHelper.FSql.Update<BetOrder>()
                            .Set(a => a.OrderType, betOrder.OrderType)
                            .Where(a => a.Id == betOrder.Id)
                            .ExecuteAffrowsAsync(token);
                    }

                    // 发送封盘提醒,处理汇总数据
                    await RobotHelper.发送封盘提醒(betOrderList);
                    await RobotHelper.处理汇总数据(betOrderList);

                    // 判断是否开启飞单,没有开启则直接播放封盘提示音后进入下一轮循环
                    if (!UserSetting.Current.是否开启飞单)
                    {
                        await DbHelper.AddLogAsync(EnumLogType.平台, "飞单提醒", $"{Ai.中括号左}{issueNow}{Ai.中括号右}期未开启飞单.");
                        SoundHelper.MediaStopBet.Play();
                        continue;
                    }

                    // 开启飞单则先判断处理对冲吃单
                    if (UserSetting.Current.是否对冲吃单)
                    {
                        await RobotHelper.处理对冲吃单();
                    }

                    // 处理完对冲吃单后从汇总表判断是否还有需要飞单的数据,
                    long count = await DbHelper.FSql.Select<HuiZong>()
                        .Where(a => a.Issue == issueNow)
                        .Where(a => a.ToBetBalance > 0)
                        .Where(a => a.BetResult == EnumBetResult.未知)
                        .CountAsync(token);

                    // 没有飞单数据则直接播放封盘提示音
                    if (count == 0)
                    {
                        await DbHelper.AddLogAsync(EnumLogType.平台, "飞单提醒", $"{Ai.中括号左}{issueNow}{Ai.中括号右}期没有飞单数据.");
                        SoundHelper.MediaStopBet.Play();
                        continue;
                    }

                    // 处理飞单
                    UserSetting.Current.LastBetIssue = issueNow; // 记录飞单期号
                    await BetHelper.AutoBet(); // 执行自动飞单
                    await BetHelper.CheckBetResult(); // 如果成功则记录成功状态,否则保留未知状态
                    await BetHelper.ShowBetResultFailed(); // 未知状态均为失败,弹出提示框

                    // 判断是否需要自动撤销
                    if (UserSetting.Current.AutoCancelOrder)
                    {
                        await BetHelper.AutoCancelBetFailedData();
                    }
                }
            }
            catch (Exception ex)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "ServiceError", ex.ToString());
            }
        }
    }

    #endregion

    #region 关闭主窗口

    /// <summary>
    /// 关闭主窗口
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void FormMain_FormClosing(object sender, FormClosingEventArgs e)
    {
        if (!UIMessageBox.ShowMessageDialog("确定要关闭程序吗？", "操作提示", true, UIStyle.Red, true))
        {
            e.Cancel = true;
            return;
        }

        CommonHelper.Cts.Cancel();
        HttpListenerHelper.Cts.Cancel();
        HttpListenerHelper.Listener.Stop();
        HttpListenerHelper.Listener.Close();
    }

    #endregion
}