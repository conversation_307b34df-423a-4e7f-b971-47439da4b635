﻿using FreeSql.DataAnnotations;
using Robot.Enum;

namespace Robot.Models;

/// <summary>
/// 余额操作订单基类 - 上分下分操作的通用数据模型
///
/// 功能说明：
/// - 作为AddMoney和SubMoney的基类
/// - 定义上分下分操作的通用字段和行为
/// - 支持订单状态管理和流程控制
/// - 提供完整的操作审计追踪
///
/// 继承关系：
/// - AddMoney : BalanceOrder（上分订单）
/// - SubMoney : BalanceOrder（下分订单）
///
/// 业务流程：
/// 用户申请 -> 订单创建 -> 管理员审核 -> 状态更新 -> 余额变动
///
/// 订单状态：
/// - 待处理：刚创建的申请订单
/// - 同意：管理员同意的订单
/// - 拒绝：管理员拒绝的订单
///
/// 设计模式：
/// - 使用继承减少代码重复
/// - 统一上分下分的数据结构
/// - 便于统一处理和查询
///
/// 数据库映射：
/// - 不直接对应数据库表
/// - 由子类AddMoney和SubMoney继承使用
/// - 提供通用的字段定义
/// </summary>
public class BalanceOrder
{
    /// <summary>
    /// 订单唯一标识 - 数据库自增主键
    ///
    /// 特点：
    /// - 长整型确保足够的ID空间
    /// - 自动递增，无需手动设置
    /// - 作为订单的唯一标识符
    ///
    /// 用途：
    /// - 订单查询和管理的主键
    /// - 关联财务记录的依据
    /// - 客服处理的订单编号
    /// </summary>
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 申请时间 - 用户提交申请的时间戳
    ///
    /// 用途：
    /// - 记录申请的具体时间
    /// - 用于订单查询和排序
    /// - 处理时效性的依据
    /// - 默认为当前系统时间
    ///
    /// 业务意义：
    /// - 体现申请的先后顺序
    /// - 支持按时间范围查询
    /// - 便于统计分析
    /// </summary>
    public DateTime Time { get; set; } = DateTime.Now;

    /// <summary>
    /// 用户账号 - 申请操作的用户标识
    ///
    /// 关联：
    /// - 对应Member表的Account字段
    /// - 用于用户订单查询
    /// - 余额变动的目标账户
    ///
    /// 用途：
    /// - 标识申请的归属用户
    /// - 支持用户个人订单查询
    /// - 余额操作的目标账户
    /// </summary>
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 操作金额 - 申请上分或下分的金额
    ///
    /// 数值含义：
    /// - 上分：用户希望增加的余额
    /// - 下分：用户希望提取的余额
    /// - 必须为正数，具体操作由订单类型决定
    ///
    /// 限制：
    /// - 通常有最低和最高限额
    /// - 下分时不能超过当前余额
    /// - 精确到小数点后2位
    ///
    /// 业务规则：
    /// - 上分：增加用户可用余额
    /// - 下分：减少用户可用余额
    /// </summary>
    public decimal Money { get; set; }

    /// <summary>
    /// 订单状态 - 申请订单的处理状态
    ///
    /// 状态流转：
    /// - 待处理：用户刚提交的申请
    /// - 同意：管理员同意并处理完成
    /// - 拒绝：管理员拒绝申请
    ///
    /// 业务影响：
    /// - 待处理：等待管理员审核
    /// - 同意：余额已变动，订单完成
    /// - 拒绝：申请被拒，余额不变
    ///
    /// 操作权限：
    /// - 只有管理员可以变更状态
    /// - 状态变更会触发相应的业务逻辑
    /// </summary>
    public EnumBalanceStatus Status { get; set; }

    /// <summary>
    /// 处理时间 - 管理员处理订单的时间戳
    ///
    /// 用途：
    /// - 记录订单被处理的具体时间
    /// - 计算订单处理时效
    /// - 审计管理员操作记录
    ///
    /// 更新时机：
    /// - 管理员同意或拒绝申请时更新
    /// - 初始值为默认时间
    /// - 体现订单的完整生命周期
    ///
    /// 业务价值：
    /// - 监控订单处理效率
    /// - 提供完整的操作时间线
    /// - 支持客服查询和解释
    /// </summary>
    public DateTime PreTime { get; set; }

    /// <summary>
    /// 来源消息ID - 申请消息的唯一标识
    ///
    /// 用途：
    /// - 关联聊天消息和申请订单
    /// - 便于问题追踪和客服处理
    /// - 防止重复申请处理
    ///
    /// 格式：
    /// - 通常为聊天平台的消息ID
    /// - 确保消息和订单的对应关系
    ///
    /// 业务价值：
    /// - 提供申请的完整上下文
    /// - 便于客服查看原始申请
    /// - 支持问题定位和处理
    /// </summary>
    public string FromMsgId { get; set; } = string.Empty;
}