﻿using FreeSql.DataAnnotations;

namespace Robot.Models;

/// <summary>
/// 下分订单模型类 - 用户申请提取账户余额的订单记录
///
/// 功能说明：
/// - 继承自BalanceOrder基类，复用通用字段和行为
/// - 专门处理用户的下分申请业务
/// - 支持完整的下分申请、审核、处理流程
/// - 提供下分操作的审计追踪
///
/// 业务场景：
/// - 用户通过聊天消息申请下分
/// - 管理员在系统中审核下分申请
/// - 系统自动更新用户账户余额
/// - 生成对应的财务变动记录
///
/// 业务流程：
/// 1. 用户发送下分申请消息
/// 2. 系统解析消息创建SubMoney订单
/// 3. 系统检查用户余额是否充足
/// 4. 管理员在界面中查看待处理订单
/// 5. 管理员同意或拒绝申请
/// 6. 系统更新订单状态和用户余额
/// 7. 生成财务记录和操作日志
///
/// 数据库表：SubMoney
/// - 继承BalanceOrder的所有字段
/// - 使用FreeSql ORM进行数据持久化
/// - 支持与Member表的关联查询
///
/// 与AddMoney的区别：
/// - SubMoney：减少用户余额（下分）
/// - AddMoney：增加用户余额（上分）
/// - 业务逻辑相似，数据结构相同
///
/// 业务规则：
/// - 下分金额不能超过用户当前余额
/// - 需要管理员审核，防止恶意操作
/// - 完整的操作日志，便于审计
/// - 状态控制，防止重复处理
///
/// 安全考虑：
/// - 余额充足性检查
/// - 管理员审核机制
/// - 完整的财务记录
/// - 防止重复提取
///
/// 使用示例：
/// - 用户发送："下分500"
/// - 系统创建SubMoney订单，金额500
/// - 检查用户余额是否≥500
/// - 管理员审核通过后，用户余额减少500
/// - 生成"申请下分"类型的财务记录
///
/// 风险控制：
/// - 大额下分可能需要特殊审核
/// - 频繁下分用户的风险监控
/// - 异常下分行为的预警机制
/// </summary>
[Table(Name = "SubMoney")]
public class SubMoney : BalanceOrder;