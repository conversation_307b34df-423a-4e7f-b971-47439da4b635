﻿using Flurl.Http;
using Robot.Config;
using Robot.Enum;
using Robot.Models;

namespace Robot.Helper;

/// <summary>
/// 开奖数据助手类
/// 负责从远程服务器获取开奖数据，并将数据保存到本地数据库
/// 支持台湾宾果和一六八飞艇两种彩票类型
/// </summary>
public static class DrawHelper
{
    /// <summary>
    /// 开奖数据API地址列表
    /// 包含多个服务器地址，用于提高数据获取的可靠性
    /// </summary>
    private static List<string> DataUrlList =>
    [
        $"{RobotSetting.Current.PlatformHost}/DrawInfo/"
    ];

    /// <summary>
    /// 获取开奖信息
    /// 获取开奖数据，并保存到数据库
    /// </summary>
    /// <returns>异步任务</returns>
    public static async Task GetDrawInfo()
    {
        foreach (string url in DataUrlList)
        {
            try
            {
                ResObj resObj = await url.WithTimeout(TimeSpan.FromSeconds(3))
                    .PostJsonAsync(new
                    {
                        CommonHelper.Lottery
                    }).ReceiveJson<ResObj>();

                // 获取数据异常，跳过
                if (resObj == null)
                {
                    continue;
                }

                // 获取数据异常，记录日志
                if (resObj.StatusCode != 0)
                {
                    await DbHelper.AddLogAsync(EnumLogType.机器人, "获取开奖信息异常", resObj.Msg);
                    continue;
                }

                // 没有数据，跳过
                if (!resObj.DrawList.Any())
                {
                    continue;
                }

                // 遍历取出数据
                List<KaiJiang> kjList = new List<KaiJiang>();
                foreach (Draw draw in resObj.DrawList)
                {
                    KaiJiang kj = new KaiJiang
                    {
                        Issue = draw.PreDrawIssue, // 期号
                        DrawNum = draw.PreDrawCode, // 开奖号码
                        Time = draw.PreDrawTime // 开奖时间
                    };
                    kjList.Add(kj);
                }

                // 按开奖期号排序kjList
                kjList = kjList.OrderBy(x => x.Issue).ToList();

                // 遍历写入数据库，避免重复数据
                foreach (KaiJiang kj in kjList)
                {
                    // 数据库中不存在该期号数据，写入数据库
                    KaiJiang? kjInDb = await DbHelper.FSql.Select<KaiJiang>()
                        .Where(a => a.Issue.Equals(kj.Issue))
                        .ToOneAsync();

                    if (kjInDb == null)
                    {
                        await DbHelper.FSql.Insert<KaiJiang>().AppendData(kj).ExecuteIdentityAsync();
                    }
                }
            }
            catch (FlurlHttpTimeoutException ex)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "获取开奖信息超时", ex.ToString());
            }
            catch (Exception ex)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "获取开奖信息异常", ex.ToString());
            }
        }
    }
}