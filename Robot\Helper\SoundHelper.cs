﻿using System.Media;

namespace Robot.Helper;

/// <summary>
/// SoundHelper, 音效播放器
/// </summary>
public static class SoundHelper
{
    #region InitSoundService,载入音效文件

    /// <summary>
    /// InitSoundService,载入音效文件
    /// </summary>
    public static void InitSoundService()
    {
        MediaStartService.Load();
        MediaStopService.Load();
        MediaRechargeWithdrawal.Load();
        MediaBetSuccess.Load();
        MediaBetFalse.Load();
        MediaOpenDraw.Load();
        MediaStopBet.Load();
    }

    #endregion

    #region 设置音效文件路径

    public static SoundPlayer MediaStartService { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/StartService.wav" };
    public static SoundPlayer MediaStopService { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/StopService.wav" };
    public static SoundPlayer MediaRechargeWithdrawal { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/RechargeWithdrawal.wav" };
    public static SoundPlayer MediaBetSuccess { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/BetSuccess.wav" };
    public static SoundPlayer MediaBetFalse { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/BetFalse.wav" };
    public static SoundPlayer MediaOpenDraw { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/OpenDraw.wav" };
    public static SoundPlayer MediaStopBet { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/StopBet.wav" };

    #endregion
}