﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <ApplicationIcon>Dynamo.ico</ApplicationIcon>
    <AssemblyVersion>10.0.0.0</AssemblyVersion>
    <FileVersion>10.0.0.0</FileVersion>
    <SatelliteResourceLanguages>xxx</SatelliteResourceLanguages>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="Dynamo.ico" />
  </ItemGroup>

  <ItemGroup>
    <!-- 原有依赖包 -->
    <PackageReference Include="Autoupdater.NET.Official" Version="1.9.2" />
    <PackageReference Include="Costura.Fody" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
    <PackageReference Include="FreeSql" Version="3.5.211" />
    <PackageReference Include="FreeSql.DbContext" Version="3.5.211" />
    <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.211" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SunnyUI" Version="3.8.7" />
    <PackageReference Include="System.Management" Version="9.0.7" />

    <!-- 新增依赖包用于架构重构 -->
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Models\PostBetData.cs" />
    <Compile Remove="Helper\ImageUploader.cs" />
    <Compile Remove="Helper\ChatApiClient.cs" />
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="Ui\FormAlertMessage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Ui\FormLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Ui\FormInputBox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Ui\FormMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Ui\FormSetParentAccount.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Ui\FormMain.DataGridViewFormat.cs">
      <DependentUpon>FormMain.cs</DependentUpon>
    </Compile>
    <Compile Update="Ui\FormMain.InitUi.cs">
      <DependentUpon>FormMain.cs</DependentUpon>
    </Compile>
    <Compile Update="Ui\FormMain.Report.cs">
      <DependentUpon>FormMain.cs</DependentUpon>
    </Compile>
    <Compile Update="Ui\FormMain.ShowInfo.cs">
      <DependentUpon>FormMain.cs</DependentUpon>
    </Compile>
    <Compile Update="Ui\FormMain.UI.cs">
      <DependentUpon>FormMain.cs</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="AiHelper">
      <HintPath>D:\TreeDLL\AiHelper.dll</HintPath>
    </Reference>
    <Reference Include="ControlHelper">
      <HintPath>D:\TreeDLL\ControlHelper.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>