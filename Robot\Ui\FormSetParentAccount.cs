﻿using System.Globalization;
using AiHelper;
using Robot.Config;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;
using Sunny.UI;

namespace Robot.Ui;

/// <summary>
/// 设置拉手上级窗体类
///
/// 功能说明：
/// 1. 为会员设置拉手上级，建立会员层级关系
/// 2. 设置返利比例，用于上级从下级的投注中获得返利
/// 3. 防止循环拉手关系的建立
///
/// 业务场景：
/// - 在会员管理界面右键点击会员，选择"设置拉手上级"时弹出此窗体
/// - 管理员可以为任意会员指定其拉手上级
/// - 拉手上级可以从下级会员的投注中获得一定比例的返利
///
/// 拉手系统说明：
/// - 拉手上级：推荐会员进入系统的上级用户，可获得下级投注的返利
/// - 返利比例：上级从下级投注中获得的返利百分比（0-2.5%）
/// - 层级关系：支持多级拉手关系，但不允许循环引用
/// </summary>
public partial class FormSetParentAccount : Form
{
    /// <summary>
    /// 构造函数 - 初始化设置拉手上级窗体
    /// </summary>
    public FormSetParentAccount()
    {
        InitializeComponent();
    }

    /// <summary>
    /// 窗体加载事件处理器
    ///
    /// 功能：
    /// 1. 窗体显示时自动加载所有会员到下拉框
    /// 2. 初始化界面数据显示
    /// 3. 异常处理和日志记录
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void FormSetParentAccount_Load(object sender, EventArgs e)
    {
        try
        {
            // 异步加载会员列表到下拉框，避免界面卡顿
            await AddMemberToComboBox(comboBox_Member);
        }
        catch (Exception ex)
        {
            // 记录异常日志，便于问题排查
            await DbHelper.AddLogAsync(EnumLogType.机器人, @"AddMemberToComboBoxError", ex.ToString());
        }
    }

    /// <summary>
    /// 将会员列表添加到下拉框并初始化选择状态
    ///
    /// 功能详解：
    /// 1. 从数据库获取所有会员信息
    /// 2. 过滤掉当前会员（不能设置自己为自己的上级）
    /// 3. 格式化显示会员信息：[账号]昵称
    /// 4. 如果当前会员已有拉手上级，自动选中对应项
    /// 5. 显示当前的返利比例
    ///
    /// 数据流程：
    /// 数据库 -> 会员列表 -> 下拉框选项 -> 界面显示
    /// </summary>
    /// <param name="comboBox">要填充的下拉框控件</param>
    private async Task AddMemberToComboBox(ComboBox comboBox)
    {
        try
        {
            // 异步从数据库获取所有会员信息
            // 使用FreeSql ORM进行数据查询，提高性能和安全性
            List<Member> memberList = await DbHelper.FSql
                .Select<Member>()
                .ToListAsync();

            // 清空下拉框并添加默认选项
            comboBox.Items.Clear();
            comboBox.Items.Add("请选择拉手上级");

            // 遍历会员列表，添加到下拉框
            foreach (Member member in memberList)
            {
                // 排除当前会员，防止设置自己为自己的上级
                if (member.Account == CommonHelper.CurrentMember.Account)
                {
                    continue;
                }

                // 格式化显示：[账号]昵称，便于管理员识别
                comboBox.Items.Add($"{Ai.中括号左}{member.Account}{Ai.中括号右}{member.昵称}");
            }

            // 判断当前会员是否已有拉手上级
            if (string.IsNullOrEmpty(CommonHelper.CurrentMember.ParentAccount))
            {
                // 没有上级时，默认选择第一项（请选择拉手上级）
                comboBox.SelectedIndex = 0;
                return;
            }

            // 如果已有拉手上级，自动选中对应的选项
            foreach (Member member in memberList)
            {
                if (member.Account == CommonHelper.CurrentMember.ParentAccount)
                {
                    // 找到匹配的上级，设置下拉框选中状态
                    comboBox.SelectedIndex = comboBox.Items.IndexOf($"{Ai.中括号左}{member.Account}{Ai.中括号右}{member.昵称}");
                    break;
                }
            }

            // 显示当前的返利比例到文本框
            // 使用InvariantCulture确保数字格式的一致性，避免本地化问题
            textBox_Rebate.Text = CommonHelper.CurrentMember.ParentRebatePercent.ToString(CultureInfo.InvariantCulture);
        }
        catch (Exception ex)
        {
            // 异常处理：记录详细错误信息到日志系统
            await DbHelper.AddLogAsync(EnumLogType.机器人, "AddMemberToComboBoxError", ex.ToString());
        }
    }

    /// <summary>
    /// 确定按钮点击事件处理器 - 保存拉手上级设置
    ///
    /// 核心业务逻辑：
    /// 1. 数据验证：检查选择和输入的有效性
    /// 2. 循环检测：防止形成循环拉手关系
    /// 3. 数据更新：更新数据库中的拉手关系和返利比例
    /// 4. 日志记录：记录操作历史便于审计
    ///
    /// 验证规则：
    /// - 必须选择拉手上级（不能为默认选项）
    /// - 返利比例必须为有效数字
    /// - 返利比例范围：0-2.5%
    /// - 不能形成循环拉手关系
    ///
    /// 业务影响：
    /// - 设置后，上级可从下级的投注中获得返利
    /// - 影响系统的返利计算和结算流程
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void button_Sure_Click(object sender, EventArgs e)
    {
        try
        {
            // 第一步：验证是否选择了拉手上级
            if (comboBox_Member.SelectedIndex.Equals(0))
            {
                MessageBox.Show(@"请选择拉手上级！");
                return;
            }

            // 第二步：从下拉框文本中提取拉手上级账号
            // 格式：[账号]昵称 -> 提取账号部分
            string parentAccount = Ai.GetTextMiddle(comboBox_Member.Text, Ai.中括号左, Ai.中括号右);

            // 第三步：循环拉手关系检测
            // 检查要设置的上级是否已经把当前会员设为其上级，防止A->B, B->A的循环关系
            Member parentMember = await DbHelper.FSql.Select<Member>()
                .Where(a => a.Account == parentAccount)
                .FirstAsync();

            if (parentMember.ParentAccount == CommonHelper.CurrentMember.Account)
            {
                MessageBox.Show($@"这个号{Ai.中括号左}{CommonHelper.CurrentMember.Account}{Ai.中括号右}已经是{Ai.中括号左}{parentAccount}{Ai.中括号右}的拉手上级，不能交叉设置！");
                return;
            }

            // 第四步：返利比例验证 - 空值检查
            string rebateStr = textBox_Rebate.Text.Trim();
            if (string.IsNullOrWhiteSpace(rebateStr))
            {
                MessageBox.Show(@"返利比例不能为空！");
                return;
            }

            // 第五步：返利比例验证 - 数字格式检查
            if (!decimal.TryParse(rebateStr, out _))
            {
                MessageBox.Show(@"返利比例输入错误，请重新输入！");
                return;
            }

            // 第六步：转换返利比例为decimal类型
            decimal rebate = Convert.ToDecimal(textBox_Rebate.Text.Trim());

            // 第七步：返利比例范围验证
            // 业务规则：返利比例必须在0-2.5%之间，防止过高的返利影响系统盈利
            if (rebate is < 0 or > (decimal)2.5)
            {
                MessageBox.Show(@"返利比例输入错误，请重新输入！参考范围为0-2.5之间");
                return;
            }

            // 第八步：更新数据库 - 保存拉手关系和返利比例
            // 使用FreeSql ORM进行安全的数据库更新操作
            // 这里同时更新两个关键字段：拉手上级账号和返利比例
            await DbHelper.FSql.Update<Member>()
                .Set(a => a.ParentAccount, parentAccount) // 设置拉手上级账号
                .Set(a => a.ParentRebatePercent, rebate) // 设置返利比例
                .Where(a => a.Account == CommonHelper.CurrentMember.Account) // 更新当前会员
                .ExecuteAffrowsAsync();

            // 第九步：记录操作日志
            // 重要：所有会员信息变更都需要记录日志，便于后续审计和问题追踪
            // 日志内容包含：操作类型、被操作会员信息、具体变更内容
            await DbHelper.AddLogAsync(EnumLogType.机器人, "操作会员",
                $"修改{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}的拉手上级为{Ai.中括号左}{parentAccount}{Ai.中括号右}");

            // 第十步：关闭窗体
            // 操作成功完成后自动关闭窗体，返回主界面

            this.ShowSuccessTip("设置成功！");
            Close();
        }
        catch (Exception ex)
        {
            // 异常处理：记录详细错误信息
            // 包含完整的异常堆栈信息，便于开发人员定位问题
            await DbHelper.AddLogAsync(EnumLogType.机器人, "button_Sure_ClickError", ex.ToString());
        }
    }
}