﻿using FreeSql.DataAnnotations;
using Robot.Enum;

namespace Robot.Models;

/// <summary>
/// 投注订单模型类 - 彩票投注系统的核心数据模型
///
/// 功能说明：
/// - 记录用户的每一笔投注订单详细信息
/// - 支持订单状态跟踪和结算处理
/// - 包含完整的投注、开奖、结算流程数据
/// - 支持回水机制和返利计算
///
/// 业务流程：
/// 用户投注 -> 订单创建 -> 开奖结算 -> 回水处理 -> 订单完成
///
/// 数据库表：BetOrder
/// - 使用FreeSql ORM进行数据持久化
/// - 支持自增主键和完整的CRUD操作
/// - 关联Member表进行用户信息管理
///
/// 订单状态流转：
/// 待处理 -> 已受理 -> 已结算 -> 已回水
///
/// 支持的彩种：
/// - 台湾宾果：宾果1、宾果2、宾果3
/// - 一六八飞艇：前3、中3、后3
/// - 新一六八XL：前3、中3、后3
/// </summary>
[Table(Name = "BetOrder")]
public class BetOrder
{
    /// <summary>
    /// 订单唯一标识 - 数据库自增主键
    ///
    /// 特点：
    /// - 长整型确保足够的ID空间
    /// - 自动递增，无需手动设置
    /// - 作为订单的唯一标识符
    /// </summary>
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 投注时间 - 订单创建的时间戳
    ///
    /// 用途：
    /// - 记录用户投注的具体时间
    /// - 用于订单查询和统计分析
    /// - 默认为当前系统时间
    /// </summary>
    public DateTime Time { get; set; } = DateTime.Now;

    /// <summary>
    /// 用户账号 - 投注用户的唯一标识
    ///
    /// 关联：
    /// - 对应Member表的Account字段
    /// - 用于用户投注记录查询
    /// - 结算时更新用户余额
    /// </summary>
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 投注期号 - 彩票开奖期号
    ///
    /// 格式：
    /// - 台湾宾果：YYYYMMDD-XXX（如：********-001）
    /// - 飞艇系列：YYYYMMDD-XXX（如：********-001）
    ///
    /// 用途：
    /// - 关联开奖数据进行结算
    /// - 按期号统计投注情况
    /// </summary>
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 投注彩种 - 具体的彩票游戏类型
    ///
    /// 枚举值：
    /// - 台湾宾果1、台湾宾果2、台湾宾果3
    /// - 一六八飞艇前3、一六八飞艇中3、一六八飞艇后3
    /// - 新一六八XL前、新一六八XL中、新一六八XL后
    ///
    /// 作用：
    /// - 决定开奖结果的计算方式
    /// - 影响赔率和结算逻辑
    /// </summary>
    public EnumBetLottery BetLottery { get; set; }

    /// <summary>
    /// 投注内容 - 用户选择的投注项目
    ///
    /// 格式：
    /// - 番摊投注：1、2、3、4（对应番摊结果）
    /// - 大小投注：大、小
    /// - 单双投注：单、双
    ///
    /// 示例：
    /// - "1" 表示投注番摊1
    /// - "大" 表示投注大
    /// - "单" 表示投注单
    /// </summary>
    public string Con { get; set; } = string.Empty;

    /// <summary>
    /// 投注金额 - 用户投注的金额
    ///
    /// 限制：
    /// - 最低限投：根据Odds表配置
    /// - 最高限投：根据Odds表配置
    /// - 精确到小数点后2位
    ///
    /// 用途：
    /// - 计算中奖金额（金额 × 赔率）
    /// - 统计用户流水
    /// </summary>
    public decimal Money { get; set; }

    /// <summary>
    /// 投注赔率 - 该投注项目的赔率
    ///
    /// 来源：
    /// - 从Odds表获取对应项目的赔率
    /// - 下注时锁定赔率，避免变动影响
    ///
    /// 计算：
    /// - 中奖金额 = 投注金额 × 赔率
    /// - 通常为小数形式（如：3.8表示1赔3.8）
    /// </summary>
    public decimal Odds { get; set; }

    /// <summary>
    /// 开奖结果 - 该期彩票的开奖结果
    ///
    /// 值范围：
    /// - 番摊结果：1、2、3、4
    /// - 开奖后由系统自动填入
    /// - 用于判断投注是否中奖
    ///
    /// 计算来源：
    /// - 根据开奖号码和彩种规则计算
    /// - 由RobotHelper.GetDrawResult()方法计算
    /// </summary>
    public int DrawResult { get; set; }

    /// <summary>
    /// 中奖状态 - 投注结果（中/挂/和）
    ///
    /// 枚举值：
    /// - 中：投注成功，获得奖金
    /// - 挂：投注失败，损失本金
    /// - 和：平局，退还本金
    ///
    /// 结算影响：
    /// - 中：余额增加（本金 × 赔率）
    /// - 挂：余额不变（已扣除本金）
    /// - 和：余额增加（退还本金）
    /// </summary>
    public EnumBetWinLose WinLose { get; set; }

    /// <summary>
    /// 结算金额 - 该笔订单的最终结算金额
    ///
    /// 计算规则：
    /// - 中奖：投注金额 × 赔率
    /// - 挂彩：-投注金额（负数表示损失）
    /// - 平局：投注金额（退还本金）
    ///
    /// 用途：
    /// - 更新用户账户余额
    /// - 财务记录和统计分析
    /// </summary>
    public decimal 结算 { get; set; }

    /// <summary>
    /// 订单状态 - 投注订单的处理状态
    ///
    /// 状态流转：
    /// - 待处理：刚创建的订单
    /// - 已受理：系统确认的有效订单
    /// - 已结算：开奖后完成结算的订单
    ///
    /// 业务意义：
    /// - 控制订单处理流程
    /// - 防止重复结算
    /// </summary>
    public EnumBetOrderStatus BetOrderStatus { get; set; }

    /// <summary>
    /// 回水状态 - 订单的回水处理状态
    ///
    /// 回水机制：
    /// - 根据用户的回水比例返还部分投注金额
    /// - 通常在结算后进行回水处理
    /// - 提高用户粘性和活跃度
    ///
    /// 状态值：
    /// - 未回水：尚未处理回水
    /// - 已回水：已完成回水处理
    /// </summary>
    public EnumBetOrderRebateStatus BetOrderRebateStatus { get; set; }

    /// <summary>
    /// 回水比例 - 该订单适用的回水百分比
    ///
    /// 来源：
    /// - 从用户的回水比例设置获取
    /// - 下注时锁定比例，避免变动影响
    ///
    /// 计算：
    /// - 回水金额 = 投注金额 × 回水比例
    /// - 通常为百分比形式（如：0.05表示5%）
    /// </summary>
    public decimal 回水比例 { get; set; }

    /// <summary>
    /// 回水金额 - 该订单实际回水的金额
    ///
    /// 计算公式：
    /// - 回水金额 = 投注金额 × 回水比例
    ///
    /// 处理时机：
    /// - 通常在订单结算后进行
    /// - 直接增加到用户账户余额
    /// - 生成对应的财务记录
    /// </summary>
    public decimal 回水金额 { get; set; }

    /// <summary>
    /// 来源消息ID - 投注消息的唯一标识
    ///
    /// 用途：
    /// - 关联聊天消息和投注订单
    /// - 便于问题追踪和客服处理
    /// - 防止重复投注处理
    ///
    /// 格式：
    /// - 通常为聊天平台的消息ID
    /// - 确保消息和订单的对应关系
    /// </summary>
    public string FromMsgId { get; set; } = string.Empty;

    /// <summary>
    /// 订单类型 - 区分真人订单和假人订单
    ///
    /// 类型说明：
    /// - 真人订单：真实用户的投注订单
    /// - 假人订单：系统模拟的投注订单
    ///
    /// 业务用途：
    /// - 统计分析时区分真实和模拟数据
    /// - 财务结算时分别处理
    /// - 默认为真人订单
    /// </summary>
    public EnumOrderType OrderType { get; set; } = EnumOrderType.真人订单;
}