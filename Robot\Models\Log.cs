﻿using FreeSql.DataAnnotations;
using Newtonsoft.Json;
using Robot.Enum;

namespace Robot.Models;

/// <summary>
/// 系统日志模型类 - 系统运行过程中的完整日志记录
///
/// 功能说明：
/// - 记录系统运行的各种事件和操作
/// - 提供完整的系统行为追踪
/// - 支持问题诊断和性能分析
/// - 便于系统维护和故障排查
///
/// 日志类型：
/// - 机器人日志：机器人操作和状态变化
/// - 平台日志：与彩票平台交互的记录
/// - 系统日志：系统内部运行状态
/// - 错误日志：异常和错误信息
///
/// 业务价值：
/// - 提供系统运行的完整记录
/// - 支持问题快速定位和解决
/// - 便于系统性能监控和优化
/// - 满足审计和合规要求
///
/// 数据库表：Log
/// - 使用FreeSql ORM进行数据持久化
/// - 支持按类型、时间、标题查询
/// - 内容字段支持大容量文本存储
///
/// 使用场景：
/// - 系统启动和关闭记录
/// - 用户操作行为记录
/// - 开奖数据处理记录
/// - 异常错误信息记录
/// - 性能监控数据记录
///
/// 设计特点：
/// - 结构化的日志信息存储
/// - 支持JSON序列化
/// - 大容量内容字段
/// - 时间戳精确记录
/// </summary>
[Table(Name = "Log")]
public class Log
{
    /// <summary>
    /// 日志记录唯一标识 - 数据库自增主键
    ///
    /// 特点：
    /// - 长整型确保足够的ID空间
    /// - 自动递增，无需手动设置
    /// - 作为日志记录的唯一标识符
    ///
    /// 用途：
    /// - 日志查询和管理的主键
    /// - 日志记录的唯一引用
    /// - 支持日志的精确定位
    /// </summary>
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 日志类型 - 日志记录的分类标识
    ///
    /// 枚举值：
    /// - 机器人：机器人相关的操作和状态
    /// - 平台：与彩票平台交互的记录
    /// - 系统：系统内部运行状态
    /// - 错误：异常和错误信息
    ///
    /// 用途：
    /// - 日志分类和过滤
    /// - 支持按类型查询日志
    /// - 便于日志分析和统计
    /// - 提高日志查找效率
    ///
    /// 业务意义：
    /// - 机器人：用户交互、消息处理等
    /// - 平台：开奖获取、数据同步等
    /// - 系统：启动关闭、配置变更等
    /// - 错误：异常捕获、错误诊断等
    /// </summary>
    public EnumLogType Type { get; set; }

    /// <summary>
    /// 记录时间 - 日志事件发生的时间戳
    ///
    /// 用途：
    /// - 记录事件发生的准确时间
    /// - 用于日志的时间排序
    /// - 支持按时间范围查询
    /// - 默认为当前系统时间
    ///
    /// 重要性：
    /// - 确保日志的时间准确性
    /// - 支持事件时间线分析
    /// - 便于问题的时间定位
    /// - 提供操作的时间依据
    /// </summary>
    public DateTime Time { get; set; } = DateTime.Now;

    /// <summary>
    /// 日志标题 - 日志事件的简要描述
    ///
    /// 用途：
    /// - 提供日志内容的简要概述
    /// - 便于日志列表的快速浏览
    /// - 支持按标题搜索日志
    /// - 提高日志的可读性
    ///
    /// 内容示例：
    /// - "用户登录"
    /// - "开奖数据获取"
    /// - "投注订单处理"
    /// - "系统异常"
    /// - "配置更新"
    ///
    /// 设计原则：
    /// - 简洁明了，一目了然
    /// - 包含关键信息
    /// - 便于分类和检索
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 日志内容 - 日志事件的详细信息
    ///
    /// 特点：
    /// - 支持JSON序列化，便于结构化数据存储
    /// - 数据库字段类型为nvarchar(4000)，支持大容量文本
    /// - 可以存储复杂的对象和异常信息
    ///
    /// 内容类型：
    /// - 操作详情：具体的操作参数和结果
    /// - 异常信息：完整的异常堆栈信息
    /// - 状态数据：系统或对象的状态信息
    /// - 业务数据：相关的业务对象信息
    ///
    /// 存储格式：
    /// - 纯文本：简单的描述信息
    /// - JSON：结构化的对象数据
    /// - 异常：完整的异常信息和堆栈
    ///
    /// 用途：
    /// - 提供事件的完整上下文
    /// - 支持问题的深入分析
    /// - 便于系统行为的理解
    /// - 协助故障排查和诊断
    ///
    /// 示例：
    /// - 用户操作："用户[张三]申请上分1000元"
    /// - 系统状态："当前在线用户数：25"
    /// - 异常信息：完整的Exception.ToString()内容
    /// </summary>
    [JsonProperty]
    [Column(DbType = "nvarchar(4000)")]
    public string Content { get; set; } = string.Empty;
}