﻿using FreeSql.DataAnnotations;

namespace Robot.Models;

/// <summary>
/// 上分订单模型类 - 用户申请增加账户余额的订单记录
///
/// 功能说明：
/// - 继承自BalanceOrder基类，复用通用字段和行为
/// - 专门处理用户的上分申请业务
/// - 支持完整的上分申请、审核、处理流程
/// - 提供上分操作的审计追踪
///
/// 业务场景：
/// - 用户通过聊天消息申请上分
/// - 管理员在系统中审核上分申请
/// - 系统自动更新用户账户余额
/// - 生成对应的财务变动记录
///
/// 业务流程：
/// 1. 用户发送上分申请消息
/// 2. 系统解析消息创建AddMoney订单
/// 3. 管理员在界面中查看待处理订单
/// 4. 管理员同意或拒绝申请
/// 5. 系统更新订单状态和用户余额
/// 6. 生成财务记录和操作日志
///
/// 数据库表：AddMoney
/// - 继承BalanceOrder的所有字段
/// - 使用FreeSql ORM进行数据持久化
/// - 支持与Member表的关联查询
///
/// 与SubMoney的区别：
/// - AddMoney：增加用户余额（上分）
/// - SubMoney：减少用户余额（下分）
/// - 业务逻辑相似，数据结构相同
///
/// 安全考虑：
/// - 需要管理员审核，防止恶意操作
/// - 完整的操作日志，便于审计
/// - 状态控制，防止重复处理
///
/// 使用示例：
/// - 用户发送："上分1000"
/// - 系统创建AddMoney订单，金额1000
/// - 管理员审核通过后，用户余额增加1000
/// - 生成"申请上分"类型的财务记录
/// </summary>
[Table(Name = "AddMoney")]
public class AddMoney : BalanceOrder;