﻿using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using AiHelper;
using Robot.Config;
using Robot.Enum;
using Robot.Models;

namespace Robot.Helper;

/// <summary>
/// 图片处理帮助类
/// 负责生成开奖数据图片和路子图，支持台湾宾果和168飞艇两种游戏类型
/// 主要功能包括：开奖数据展示图、路子图（固定行列和动态列数）、图片缩放和合并
/// </summary>
public static class ImageHelper
{
    #region 静态属性 - 图片保存路径配置

    /// <summary>
    /// 开奖数据图片保存路径
    /// </summary>
    public static string DrawImagePath { get; set; } = $"{AppDomain.CurrentDomain.BaseDirectory}/images/draw.jpeg";

    /// <summary>
    /// 7行路子图保存路径
    /// </summary>
    public static string TanRows7ImagePath { get; set; } = $"{AppDomain.CurrentDomain.BaseDirectory}/images/tan7.jpeg";

    /// <summary>
    /// 6行路子图保存路径
    /// </summary>
    public static string TanRows6ImagePath { get; set; } = $"{AppDomain.CurrentDomain.BaseDirectory}/images/tan6.jpeg";

    /// <summary>
    /// 7行路子图保存路径（备用）
    /// </summary>
    public static string TanRows77ImagePath { get; set; } = $"{AppDomain.CurrentDomain.BaseDirectory}/images/tan77.jpeg";

    /// <summary>
    /// 6行路子图保存路径（备用）
    /// </summary>
    public static string TanRows66ImagePath { get; set; } = $"{AppDomain.CurrentDomain.BaseDirectory}/images/tan66.jpeg";

    #endregion

    #region 静态资源缓存

    /// <summary>
    /// 图片资源路径缓存字典
    /// 预加载所有需要使用的图片文件路径，避免重复查找文件
    /// </summary>
    private static Dictionary<string, string> ImagePaths { get; } = new()
    {
        ["titleBg"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "titleBg.png")[0], // 标题背景图
        ["title"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "title.png")[0], // 标题
        ["detail"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "detail.png")[0], // 详情行背景图
        ["11"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "11.png")[0], // 番摊结果1图标
        ["22"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "22.png")[0], // 番摊结果2图标
        ["33"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "33.png")[0], // 番摊结果3图标
        ["44"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "44.png")[0], // 番摊结果4图标
        ["yellow"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "yellow.png")[0], // 黄色号码背景图
        ["0"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "0.png")[0], // 路子图数字0图标
        ["1"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "1.png")[0], // 路子图数字1图标
        ["2"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "2.png")[0], // 路子图数字2图标
        ["3"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "3.png")[0], // 路子图数字3图标
        ["4"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "4.png")[0], // 路子图数字4图标
        ["new0"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "new0.png")[0], // 路子图数字0图标
        ["new1"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "new1.png")[0], // 路子图数字1图标
        ["new2"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "new2.png")[0], // 路子图数字2图标
        ["new3"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "new3.png")[0], // 路子图数字3图标
        ["new4"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "new4.png")[0] // 路子图数字4图标
    };

    /// <summary>
    /// 游戏配置字典
    /// 包含每种游戏类型的显示名称和启用状态检查函数
    /// 用于统一管理所有支持的游戏类型及其配置
    /// </summary>
    private static readonly Dictionary<EnumBetLottery, (string name, Func<bool> isEnabled)> GameConfigs = new()
    {
        [EnumBetLottery.台湾宾果1] = ("台湾宾果1", () => UserSetting.Current.启用台湾宾果1.Equals(1)),
        [EnumBetLottery.台湾宾果2] = ("台湾宾果2", () => UserSetting.Current.启用台湾宾果2.Equals(1)),
        [EnumBetLottery.台湾宾果3] = ("台湾宾果3", () => UserSetting.Current.启用台湾宾果3.Equals(1)),
        [EnumBetLottery.一六八飞艇前3] = ("168飞艇前3", () => UserSetting.Current.启用168飞艇前3.Equals(1)),
        [EnumBetLottery.一六八飞艇中3] = ("168飞艇中3", () => UserSetting.Current.启用168飞艇中3.Equals(1)),
        [EnumBetLottery.一六八飞艇后3] = ("168飞艇后3", () => UserSetting.Current.启用168飞艇后3.Equals(1))
    };

    #endregion

    #region 私有通用方法

    /// <summary>
    /// 获取并处理开奖数据
    /// 根据游戏类型获取指定时间段或期号段的开奖数据，并进行数据补全和数量限制处理
    /// </summary>
    /// <param name="kj">基准开奖数据，用于确定查询范围</param>
    /// <param name="emptyDrawNum">空开奖号码字符串，用于补全缺失数据</param>
    /// <returns>处理后的开奖数据列表，按期号倒序排列</returns>
    private static async Task<List<KaiJiang>> GetProcessedKaiJiangData(KaiJiang kj, string emptyDrawNum)
    {
        // 根据游戏类型确定数据查询标识
        // 台湾宾果：使用时间前10位（日期）作为查询条件
        // 168飞艇：使用期号前8位作为查询条件
        string getDataFlag = CommonHelper.Lottery == EnumLottery.台湾宾果
            ? kj.Time.Substring(0, 10)
            : kj.Issue.Substring(0, 8);

        // 从数据库查询对应时间段或期号段的开奖数据
        var kjList = await DbHelper.FSql.Select<KaiJiang>()
            .Where(x => CommonHelper.Lottery == EnumLottery.台湾宾果
                ? x.Time.StartsWith(getDataFlag)
                : x.Issue.StartsWith(getDataFlag))
            .ToListAsync();

        // 按期号升序排列
        kjList = kjList.OrderBy(x => x.Issue).ToList();

        // 补全缺失的期号数据
        FillEmptyData(kjList, emptyDrawNum);
        return kjList;
    }

    /// <summary>
    /// 补全空数据
    /// 检查开奖数据列表中是否有缺失的期号，如果有则插入空数据进行补全
    /// 确保期号连续性，避免图表显示时出现空白或错位
    /// </summary>
    /// <param name="kjList">开奖数据列表</param>
    /// <param name="emptyDrawNum">空开奖号码字符串</param>
    private static void FillEmptyData(List<KaiJiang> kjList, string emptyDrawNum)
    {
        for (int i = 0; i < kjList.Count - 1; i++)
        {
            // 检查相邻两个期号是否连续
            if (Convert.ToInt64(kjList[i].Issue) + 1 != Convert.ToInt64(kjList[i + 1].Issue))
            {
                // 插入缺失的期号数据
                kjList.Insert(i + 1, new KaiJiang
                {
                    Issue = (Convert.ToInt64(kjList[i].Issue) + 1).ToString(),
                    DrawNum = emptyDrawNum,
                    Time = Ai.GetTextLeft(kjList[i].Time, " ") + " " + @"00:00:00"
                });
                i--; // 重新检查当前位置，确保连续补全
            }
        }
    }

    /// <summary>
    /// 合并多张图片为一张垂直排列的图片
    /// 将多个游戏类型的图片垂直合并成一张完整的图片
    /// </summary>
    /// <param name="images">要合并的图片列表</param>
    /// <returns>合并后的图片</returns>
    private static Task<Bitmap> MergeImages(List<Bitmap> images)
    {
        if (!images.Any()) return Task.FromResult(new Bitmap(1, 1));

        // 计算合并后图片的总高度和最大宽度
        int totalHeight = images.Sum(img => img.Height);
        int maxWidth = images.Max(img => img.Width);

        // 创建合并后的图片画布
        var mergedImage = new Bitmap(maxWidth, totalHeight);
        using var g = Graphics.FromImage(mergedImage);

        // 垂直排列绘制每张图片
        int currentHeight = 0;
        foreach (var img in images)
        {
            g.DrawImage(img, new Rectangle(0, currentHeight, img.Width, img.Height));
            currentHeight += img.Height;
        }

        return Task.FromResult(mergedImage);
    }

    /// <summary>
    /// 图片缩放处理
    /// 将原始图片按指定比例缩放，并添加随机透明图形以增加图片的不可预测性
    /// 主要用于减小图片文件大小，便于网络传输和存储
    /// 同时通过添加随机元素，避免图片被简单的数字指纹识别
    /// </summary>
    /// <param name="original">原始位图对象</param>
    /// <param name="scaleFactor">缩放比例，范围0.0-1.0，例如0.25表示缩放到原尺寸的25%</param>
    /// <returns>缩放后的位图对象</returns>
    private static Task<Bitmap> ChangeScaledImage(Bitmap original, float scaleFactor)
    {
        // 计算缩放后的新尺寸
        int newWidth = (int)(original.Width * scaleFactor);
        int newHeight = (int)(original.Height * scaleFactor);

        // 创建新的位图画布
        var scaledBmp = new Bitmap(newWidth, newHeight);
        using var g = Graphics.FromImage(scaledBmp);

        // 设置高质量的插值模式，确保缩放后图片保持清晰
        // HighQualityBicubic：使用高质量双三次插值算法，适合图片缩放
        g.InterpolationMode = InterpolationMode.HighQualityBicubic;
        g.DrawImage(original, 0, 0, newWidth, newHeight);

        // 添加随机透明图形，增加图片的不可预测性
        // 这些图形完全透明（Alpha=0），不影响视觉效果
        // 但会改变图片的数字指纹，防止被简单的图片识别算法检测
        var rand = new Random();
        for (int i = 0; i < 100; i++)
        {
            // 随机生成矩形的位置和尺寸
            int x = rand.Next(newWidth);
            int y = rand.Next(newHeight);
            int width = rand.Next(1, 10); // 小尺寸矩形，减少对性能的影响
            int height = rand.Next(1, 10);

            // 创建完全透明的随机颜色（Alpha=0表示完全透明）
            var color = Color.FromArgb(0, rand.Next(256), rand.Next(256), rand.Next(256));
            using var pen = new Pen(color);
            g.DrawRectangle(pen, x, y, width, height);
        }

        return Task.FromResult(scaledBmp);
    }

    #endregion

    #region 公共方法-绘制开奖图

    /// <summary>
    /// 绘制开奖数据图片
    /// 生成包含最近开奖数据的详细图片，显示期号、时间、开奖号码、总和、番摊结果和大小单双
    /// 支持多种游戏类型同时生成，最终合并为一张图片
    /// </summary>
    /// <param name="kj">基准开奖数据，用于确定查询范围</param>
    /// <param name="filePath">图片保存路径</param>
    /// <returns>异步任务</returns>
    public static async Task DrawOpenDataAsync(KaiJiang kj, string filePath)
    {
        var images = new List<Bitmap>();

        try
        {
            // 根据游戏类型确定数据参数
            // 台湾宾果：最多20条数据，21个0的空号码
            // 168飞艇：最多6条数据，10个0的空号码
            var maxCount = CommonHelper.Lottery == EnumLottery.台湾宾果 ? 20 : 6;
            var emptyDrawNum = CommonHelper.Lottery == EnumLottery.台湾宾果 ? "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0" : "0,0,0,0,0,0,0,0,0,0";

            // 获取处理后的开奖数据
            var kjList = await GetProcessedKaiJiangData(kj, emptyDrawNum);

            // 删除多余数据
            while (kjList.Count > maxCount)
            {
                kjList.RemoveAt(0);
            }

            // 开奖图需要倒序排列，最新数据在前
            kjList.Reverse();

            // 获取所有启用的游戏类型
            var enabledGames = GameConfigs.Where(g => g.Value.isEnabled()).ToList();

            // 为每个启用的游戏类型生成图片
            foreach (var game in enabledGames)
            {
                var bitmap = await CreateOpenDataBitmap(kjList, game.Key);
                // if (bitmap != null)
                // {
                //     images.Add(bitmap);
                // }

                if (bitmap != null)
                {
                    // 缩放图片大小
                    var scaledBitmap = await ChangeScaledImage(bitmap, 0.25f);
                    images.Add(scaledBitmap);
                    bitmap.Dispose();
                }
            }

            // 合并所有图片并保存
            if (images.Any())
            {
                var mergedImage = await MergeImages(images);
                mergedImage.Save(filePath, ImageFormat.Jpeg);
                mergedImage.Dispose();
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "DrawOpenDataAsync", ex.ToString());
        }
        finally
        {
            // 清理所有图片资源
            foreach (var img in images)
            {
                img.Dispose();
            }
        }
    }

    /// <summary>
    /// 创建单个游戏类型的开奖数据图片
    /// 生成包含标题和多行开奖数据的图片
    /// </summary>
    /// <param name="kjList">开奖数据列表</param>
    /// <param name="gameType">游戏类型</param>
    /// <returns>生成的图片，如果数据为空则返回null</returns>
    private static async Task<Bitmap?> CreateOpenDataBitmap(List<KaiJiang> kjList, EnumBetLottery gameType)
    {
        if (!kjList.Any()) return null;

        // 创建画布，高度根据数据行数动态计算
        var bmp = new Bitmap(2500, 130 + 145 * kjList.Count);
        using var g = Graphics.FromImage(bmp);
        g.Clear(Color.White);

        // 绘制头部标题背景
        using var titleImg = Image.FromFile(ImagePaths["title"]);
        g.DrawImage(titleImg, 0, 0, titleImg.Width, titleImg.Height);

        // 绘制每行开奖数据
        for (int i = 0; i < kjList.Count; i++)
        {
            await DrawOpenDataRow(g, kjList[i], gameType, i);
        }

        return bmp;
    }

    /// <summary>
    /// 绘制单行开奖数据
    /// 在指定位置绘制一期开奖数据的详细信息
    /// </summary>
    /// <param name="g">图形绘制对象</param>
    /// <param name="kj">开奖数据</param>
    /// <param name="gameType">游戏类型</param>
    /// <param name="rowIndex">行索引</param>
    private static async Task DrawOpenDataRow(Graphics g, KaiJiang kj, EnumBetLottery gameType, int rowIndex)
    {
        int y = 130 + 145 * rowIndex; // 计算当前行的Y坐标

        // 绘制详情栏背景图
        using var detailImg = Image.FromFile(ImagePaths["detail"]);
        g.DrawImage(detailImg, 0, y, detailImg.Width, detailImg.Height);

        // 设置字体和颜色
        using var font = new Font("微软雅黑", 40, FontStyle.Bold);
        var textColor = new SolidBrush(Color.FromArgb(255, 79, 38, 13));

        // 绘制开奖期号
        g.DrawString(kj.Issue, font, textColor, 50, y + 30);

        // 检查是否有有效的开奖数据
        var emptyDrawNum = CommonHelper.Lottery == EnumLottery.台湾宾果
            ? "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0"
            : "0,0,0,0,0,0,0,0,0,0";

        if (kj.DrawNum == emptyDrawNum) return; // 如果是空数据则跳过

        // 绘制开奖时间（去掉秒数）
        string? tmpTime = Ai.GetTextRight(kj.Time, "-");
        tmpTime = tmpTime.Substring(0, tmpTime.Length - 3);
        g.DrawString(tmpTime, font, textColor, 500, y + 30);

        // 获取该游戏类型的开奖号码和结果
        string drawNum = await RobotHelper.GetDrawNum(kj, gameType);
        int drawResult = await RobotHelper.GetDrawResult(kj, gameType);

        // 绘制开奖号码
        await DrawOpenDataNumbers(g, drawNum, gameType, y, font, textColor);

        // 计算并绘制号码总和
        var nums = drawNum.Split(',');
        int sum = nums.Sum(Convert.ToInt32);
        g.DrawString(sum.ToString(), font, textColor, 1865, y + 30);

        // 绘制番摊结果图标和大小单双文字
        DrawResultIcon(g, drawResult, 2010, y, font);
    }

    /// <summary>
    /// 绘制开奖号码
    /// 根据游戏类型和号码数量，在合适的位置绘制开奖号码
    /// </summary>
    /// <param name="g">图形绘制对象</param>
    /// <param name="drawNum">开奖号码字符串</param>
    /// <param name="gameType">游戏类型</param>
    /// <param name="y">绘制Y坐标</param>
    /// <param name="font">字体</param>
    /// <param name="textColor">文字颜色</param>
    private static Task DrawOpenDataNumbers(Graphics g, string drawNum, EnumBetLottery gameType, int y, Font font, SolidBrush textColor)
    {
        var nums = drawNum.Split(',');

        // 根据游戏类型和号码数量决定绘制方式
        if (gameType == EnumBetLottery.台湾宾果1)
        {
            // 台湾宾果1只显示第一个号码
            using var yellowImg = Image.FromFile(ImagePaths["yellow"]);
            g.DrawImage(yellowImg, 1590, y, yellowImg.Width, yellowImg.Height);
            g.DrawString(nums[0], font, textColor, 1660, y + 30);
        }
        else if (nums.Length == 3)
        {
            // 台湾宾果2和168飞艇显示3个号码（前3、中3、后3）
            using var yellowImg = Image.FromFile(ImagePaths["yellow"]);
            g.DrawImage(yellowImg, 990, y, yellowImg.Width, yellowImg.Height);
            g.DrawImage(yellowImg, 1290, y, yellowImg.Width, yellowImg.Height);
            g.DrawImage(yellowImg, 1590, y, yellowImg.Width, yellowImg.Height);

            g.DrawString(nums[0], font, textColor, 1070, y + 30);
            g.DrawString(nums[1], font, textColor, 1360, y + 30);
            g.DrawString(nums[2], font, textColor, 1660, y + 30);
        }
        else if (nums.Length == 1)
        {
            // 显示1个号码
            using var yellowImg = Image.FromFile(ImagePaths["yellow"]);
            g.DrawImage(yellowImg, 1590, y, yellowImg.Width, yellowImg.Height);
            g.DrawString(nums[0], font, textColor, 1660, y + 30);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 绘制开奖结果图标和大小单双文字
    /// 根据番摊结果绘制对应的图标，并在指定位置显示大小单双文字
    /// </summary>
    /// <param name="g">图形绘制对象</param>
    /// <param name="drawResult">开奖结果（1-4分别对应不同的番摊结果）</param>
    /// <param name="x">绘制起始X坐标</param>
    /// <param name="y">绘制起始Y坐标</param>
    /// <param name="font">文字字体</param>
    private static void DrawResultIcon(Graphics g, int drawResult, int x, int y, Font font)
    {
        // 根据开奖结果选择对应的图标
        var iconPath = drawResult switch
        {
            1 => ImagePaths["11"], // 番摊结果1
            2 => ImagePaths["22"], // 番摊结果2
            3 => ImagePaths["33"], // 番摊结果3
            4 => ImagePaths["44"], // 番摊结果4
            _ => null
        };

        // 绘制结果图标
        if (iconPath != null)
        {
            using var img = Image.FromFile(iconPath);
            g.DrawImage(img, x, y, img.Width, img.Height);
        }

        // 确定大小文字和颜色（1,2为小；3,4为大）
        var (sizeText, sizeColor) = drawResult switch
        {
            1 or 2 => ("小", Color.FromArgb(255, 79, 38, 13)),
            3 or 4 => ("大", Color.FromArgb(255, 255, 0, 0)),
            _ => ("", Color.Black)
        };

        // 确定单双文字和颜色（1,3为单；2,4为双）
        var (parityText, parityColor) = drawResult switch
        {
            1 or 3 => ("单", Color.FromArgb(255, 79, 38, 13)),
            2 or 4 => ("双", Color.FromArgb(255, 255, 0, 0)),
            _ => ("", Color.Black)
        };

        // 绘制大小单双文字
        if (!string.IsNullOrEmpty(sizeText))
        {
            g.DrawString(sizeText, font, new SolidBrush(sizeColor), x + 225, y + 30);
            g.DrawString(parityText, font, new SolidBrush(parityColor), x + 375, y + 30);
        }
    }

    /// <summary>
    /// 绘制路子图中的数字图标
    /// 在路子图中绘制对应数字的图标，用于显示开奖结果的走势
    /// </summary>
    /// <param name="g">图形绘制对象</param>
    /// <param name="drawResult">开奖结果数字（0-4）</param>
    /// <param name="x">绘制X坐标</param>
    /// <param name="y">绘制Y坐标</param>
    private static void DrawNumberIcon(Graphics g, int drawResult, int x, int y)
    {
        if (UserSetting.Current.ImgType.Equals(1))
        {
            // 根据结果数字选择对应的图标
            var iconPath = drawResult switch
            {
                0 => ImagePaths["0"], // 无结果或空数据
                1 => ImagePaths["1"], // 结果1
                2 => ImagePaths["2"], // 结果2
                3 => ImagePaths["3"], // 结果3
                4 => ImagePaths["4"], // 结果4
                _ => ImagePaths["0"] // 默认显示0
            };

            using var img = Image.FromFile(iconPath);
            g.DrawImage(img, x, y, 149, img.Height);
        }
        else if (UserSetting.Current.ImgType.Equals(2))
        {
            // 根据结果数字选择对应的图标
            var iconPath = drawResult switch
            {
                0 => ImagePaths["new0"], // 无结果或空数据
                1 => ImagePaths["new1"], // 结果1
                2 => ImagePaths["new2"], // 结果2
                3 => ImagePaths["new3"], // 结果3
                4 => ImagePaths["new4"], // 结果4
                _ => ImagePaths["new0"] // 默认显示0
            };

            using var img = Image.FromFile(iconPath);
            g.DrawImage(img, x, y, 149, img.Height);
        }
    }

    #endregion

    #region 公共方法-绘制路子图

    /// <summary>
    /// 绘制路子图（固定行列）
    /// 生成固定13列指定行数的路子图，显示开奖结果的走势规律
    /// 适用于标准的路子图展示，列数固定为13列
    /// </summary>
    /// <param name="kj">基准开奖数据，用于确定查询范围</param>
    /// <param name="rows">路子图行数</param>
    /// <param name="filePath">图片保存路径</param>
    /// <returns>异步任务</returns>
    public static async Task DrawTanImageAsync(KaiJiang kj, int rows, string filePath)
    {
        var images = new List<Bitmap>();

        try
        {
            // 根据游戏类型确定数据参数
            // 数据量 = 行数 × 12（预留一些数据确保填满网格）
            var (maxCount, emptyDrawNum) = CommonHelper.Lottery == EnumLottery.台湾宾果
                ? (rows * 12, "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0")
                : (rows * 12, "0,0,0,0,0,0,0,0,0,0");

            // 获取路子图专用的处理数据
            var kjList = await GetProcessedKaiJiangData(kj, emptyDrawNum);

            // 删除多余数据
            while (kjList.Count > maxCount)
            {
                kjList.RemoveRange(0, rows);
            }

            // 获取所有启用的游戏类型
            var enabledGames = GameConfigs.Where(g => g.Value.isEnabled()).ToList();

            // 为每个启用的游戏类型生成路子图
            foreach (var game in enabledGames)
            {
                // 固定13列的路子图
                var bitmap = await CreateTanImageBitmap(kjList, game.Key, game.Value.name, rows, 13);
                // if (bitmap != null)
                // {
                //     images.Add(bitmap);
                // }

                if (bitmap != null)
                {
                    // 缩放图片大小
                    var scaledBitmap = await ChangeScaledImage(bitmap, 0.25f);
                    images.Add(scaledBitmap);
                    bitmap.Dispose();
                }
            }

            // 合并所有图片并保存
            if (images.Any())
            {
                if (UserSetting.Current.ImageTitleBg.Equals(1))
                {
                    // 读入图片转为Bitmap
                    Bitmap titleBg = new Bitmap(ImagePaths["titleBg"]);
                    // // 缩放图片大小
                    var scaledImg = await ChangeScaledImage(titleBg, 0.25f);
                    images.Insert(0, scaledImg);
                }

                // 合并图片
                var mergedImage = await MergeImages(images);
                mergedImage.Save(filePath, ImageFormat.Jpeg);
                mergedImage.Dispose();
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "DrawTanImageAsync", ex.ToString());
        }
        finally
        {
            // 清理所有图片资源
            foreach (var img in images)
            {
                img.Dispose();
            }
        }
    }

    /// <summary>
    /// 绘制完整路子图（动态列数）
    /// 生成包含当天所有开奖数据的路子图，列数根据数据量动态计算
    /// 适用于需要显示完整历史数据的场景
    /// </summary>
    /// <param name="kj">基准开奖数据，用于确定查询范围</param>
    /// <param name="rows">路子图行数</param>
    /// <param name="filePath">图片保存路径</param>
    /// <returns>异步任务</returns>
    public static async Task DrawTanImageFullAsync(KaiJiang kj, int rows, string filePath)
    {
        var images = new List<Bitmap>();

        try
        {
            // 获取完整数据（不限制数量）
            var emptyDrawNum = CommonHelper.Lottery == EnumLottery.台湾宾果
                ? "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0"
                : "0,0,0,0,0,0,0,0,0,0";

            // 获取当天所有开奖数据
            var kjList = await GetProcessedKaiJiangData(kj, emptyDrawNum);

            // 根据数据量和行数计算列数
            int columns = kjList.Count / rows + 1;

            // 获取所有启用的游戏类型
            var enabledGames = GameConfigs.Where(g => g.Value.isEnabled()).ToList();

            // 为每个启用的游戏类型生成路子图
            foreach (var game in enabledGames)
            {
                // 动态列数的路子图
                var bitmap = await CreateTanImageBitmap(kjList, game.Key, game.Value.name, rows, columns);
                // if (bitmap != null)
                // {
                //     images.Add(bitmap);
                // }

                if (bitmap != null)
                {
                    // 缩放图片大小
                    var scaledBitmap = await ChangeScaledImage(bitmap, 0.25f);
                    images.Add(scaledBitmap);
                    bitmap.Dispose();
                }
            }

            // 合并所有图片并保存
            if (images.Any())
            {
                var mergedImage = await MergeImages(images);
                mergedImage.Save(filePath, ImageFormat.Jpeg);
                mergedImage.Dispose();
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "DrawTanImageFullAsync", ex.ToString());
        }
        finally
        {
            // 清理所有图片资源
            foreach (var img in images)
            {
                img.Dispose();
            }
        }
    }

    /// <summary>
    /// 创建路子图
    /// 生成指定行列数的路子图，以网格形式显示开奖结果的走势规律
    /// 路子图是彩票分析中常用的图表，用于观察号码的出现规律和趋势
    /// </summary>
    /// <param name="kjList">开奖数据列表</param>
    /// <param name="gameType">游戏类型枚举</param>
    /// <param name="gameName">游戏显示名称</param>
    /// <param name="rows">路子图行数</param>
    /// <param name="columns">路子图列数</param>
    /// <returns>生成的路子图位图，如果数据为空则返回null</returns>
    private static async Task<Bitmap?> CreateTanImageBitmap(List<KaiJiang> kjList, EnumBetLottery gameType, string gameName, int rows, int columns)
    {
        if (!kjList.Any()) return null;

        // 计算画布尺寸
        // 宽度计算：左边距(5) + 列数×图标宽度(149) + 列间距(5×列数+1) + 右边距(5+5)
        int width = 5 + 149 * columns + 5 * (columns + 1) + 5 + 5;
        // 高度计算：上边距(5) + 标题高度(140) + 标题下边距(5+5) + 行数×图标高度(140) + 行间距(5×行数+1) + 下边距(5)
        int height = 5 + 140 + 5 + 5 + 140 * rows + 5 * (rows + 1) + 5;

        var bmp = new Bitmap(width, height);
        using var g = Graphics.FromImage(bmp);

        // 绘制三层背景色，营造层次感
        g.FillRectangle(new SolidBrush(Color.FromArgb(255, 4, 181, 233)), 0, 0, bmp.Width, bmp.Height); // 整体背景（蓝色）
        g.FillRectangle(new SolidBrush(Color.FromArgb(255, 41, 146, 101)), 5, 5, bmp.Width - 12, 140); // 标题区背景（深绿色）
        g.FillRectangle(new SolidBrush(Color.FromArgb(255, 9, 236, 139)), 5, 145 + 5, bmp.Width - 12, 140 * rows + 5 * (rows + 1) + 2); // 数据区背景（浅绿色）

        // 绘制标题文字
        // 显示期号范围和游戏名称，格式：[ 起始期号 - 结束期号 ]      游戏名称
        string issue = $"[ {kjList[0].Issue} - {kjList.Last().Issue} ]      {gameName.Replace("台湾", "").Replace("宾果", "槟菓").Replace("飞艇", "")}";
        using var font = new Font("微软雅黑", 50, FontStyle.Bold);
        g.DrawString(issue, font, new SolidBrush(Color.White), 50, 25);

        // 绘制开奖号码网格
        // 采用列优先的填充方式：从左到右，每列从上到下填充
        // 这种方式符合路子图的传统显示习惯
        int index = -1;
        for (int i = 1; i <= columns; i++) // 遍历每一列
        {
            for (int j = 1; j <= rows; j++) // 遍历当前列的每一行
            {
                index++;

                // 计算当前网格位置的坐标
                int x = 149 * (i - 1) + 5 + 5 * i + 1; // X坐标：前面列的宽度 + 左边距 + 列间距 + 微调
                int y = 145 * j + 5 + 5 + 1; // Y坐标：前面行的高度 + 标题高度 + 行间距 + 微调

                // 如果数据不足，显示空图标（数字0）
                if (index >= kjList.Count)
                {
                    DrawNumberIcon(g, 0, x, y);
                    continue;
                }

                // 检查当前数据是否为空数据
                var emptyDrawNum = CommonHelper.Lottery == EnumLottery.台湾宾果
                    ? "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0" // 台湾宾果：21个0
                    : "0,0,0,0,0,0,0,0,0,0"; // 168飞艇：10个0

                if (kjList[index].DrawNum == emptyDrawNum)
                {
                    DrawNumberIcon(g, 0, x, y); // 显示空图标
                    continue;
                }

                // 获取该期开奖的番摊结果并绘制对应图标
                int drawResult = await RobotHelper.GetDrawResult(kjList[index], gameType);
                DrawNumberIcon(g, drawResult, x, y);
            }
        }

        return bmp;
    }

    #endregion
}