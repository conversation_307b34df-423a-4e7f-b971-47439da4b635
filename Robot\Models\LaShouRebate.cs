﻿namespace Robot.Models;

public class LaShouRebate
{
    public string ParentAccount { get; set; } = string.Empty;
    public string ParentName { get; set; } = string.Empty;
    public string Account { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public decimal BetAmount { get; set; } // 有效流水
    public decimal Rebate { get; set; } // 返利比例
    public decimal RebateAmount { get; set; } // 返利总额
    public string CreateTime { get; set; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff");
}