{"format": 1, "restore": {"F:\\SolutionRobotCopy\\Robot\\Robot.csproj": {}}, "projects": {"F:\\SolutionRobotCopy\\Robot\\Robot.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\SolutionRobotCopy\\Robot\\Robot.csproj", "projectName": "Robot", "projectPath": "F:\\SolutionRobotCopy\\Robot\\Robot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\SolutionRobotCopy\\Robot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Autoupdater.NET.Official": {"target": "Package", "version": "[1.9.2, )"}, "Costura.Fody": {"suppressParent": "All", "target": "Package", "version": "[6.0.0, )"}, "Flurl.Http": {"target": "Package", "version": "[4.0.2, )"}, "FreeSql": {"target": "Package", "version": "[3.5.211, )"}, "FreeSql.DbContext": {"target": "Package", "version": "[3.5.211, )"}, "FreeSql.Provider.Sqlite": {"target": "Package", "version": "[3.5.211, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SunnyUI": {"target": "Package", "version": "[3.8.7, )"}, "System.Management": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}