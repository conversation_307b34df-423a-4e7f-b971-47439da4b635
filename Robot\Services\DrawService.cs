﻿using AiHelper;
using Robot.Config;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;

namespace Robot.Services;

/// <summary>
/// 开奖服务类 - 彩票开奖信息处理的核心业务服务
///
/// 主要职责：
/// 1. 开奖信息监控：实时监控新开奖数据的产生
/// 2. 开奖结果计算：根据不同彩种计算各种投注玩法的开奖结果
/// 3. 图片生成服务：生成开奖数据图和路子图供群发使用
/// 4. 数据结算触发：触发答题数据和飞单数据的自动结算
/// 5. 消息推送控制：控制开奖图片的自动发送
///
/// 业务流程：
/// 监控开奖 -> 计算结果 -> 生成图片 -> 触发结算 -> 推送消息
///
/// 支持的彩种：
/// - 台湾宾果：支持宾果1、宾果2、宾果3三种玩法
/// - 一六八飞艇：支持前3、中3、后3三种玩法
/// - 新一六八XL：支持前3、中3、后3三种玩法
///
/// 技术特点：
/// - 异步处理避免阻塞主线程
/// - 增量检测避免重复处理
/// - 支持取消令牌的优雅停止
/// - 完整的异常处理和日志记录
///
/// 调用场景：
/// - 在FormMain的主服务循环中被定期调用
/// - 作为系统核心业务流程的重要环节
/// </summary>
public static class DrawService
{
    /// <summary>
    /// 处理开奖信息 - 系统开奖处理的核心方法
    ///
    /// 核心逻辑：
    /// 1. 增量检测：通过比较开奖数据数量判断是否有新开奖
    /// 2. 结果计算：根据不同彩种计算各种玩法的开奖结果
    /// 3. 日志记录：记录各种玩法的开奖结果到系统日志
    /// 4. 图片生成：生成开奖数据图和路子图
    /// 5. 数据结算：触发答题和飞单数据的自动结算
    /// 6. 消息推送：根据配置自动发送开奖图片到群聊
    ///
    /// 增量检测机制：
    /// - 获取处理前的开奖数据总数
    /// - 调用DrawHelper获取最新开奖信息
    /// - 比较处理后的数据总数
    /// - 只有数量增加时才进行后续处理
    ///
    /// 性能优化：
    /// - 避免重复处理相同的开奖数据
    /// - 异步操作提高系统响应性
    /// - 支持取消令牌的中断机制
    /// </summary>
    /// <param name="token">取消令牌，用于优雅停止处理流程</param>
    public static async Task<bool> ProcessDrawInfo(CancellationToken token)
    {
        // 第一步：记录处理前的开奖数据总数
        // 通过数量对比实现增量检测，避免重复处理相同数据
        long oldKaiJiangCount = await DbHelper.FSql.Select<KaiJiang>().CountAsync(token);

        // 第二步：获取最新开奖信息
        // DrawHelper.GetDrawInfo()会从平台获取最新开奖数据并保存到数据库
        await DrawHelper.GetDrawInfo();

        // 第三步：获取处理后的开奖数据总数
        long newKaiJiangCount = await DbHelper.FSql.Select<KaiJiang>().CountAsync(token);

        // 第四步：增量检测 - 判断是否有新开奖数据
        if (newKaiJiangCount > oldKaiJiangCount)
        {
            // 第五步：获取最新开奖数据
            // 按期号降序排列，取出最新的一期开奖数据
            KaiJiang lastKj = await DbHelper.FSql.Select<KaiJiang>()
                .OrderByDescending(x => x.Issue)
                .FirstAsync(token);

            // 第六步：根据不同彩种计算开奖结果并记录日志
            // 台湾宾果彩种处理
            if (CommonHelper.Lottery.Equals(EnumLottery.台湾宾果))
            {
                // 宾果1玩法：取最后一个号码进行番摊计算
                int result = await RobotHelper.GetDrawResult(lastKj, EnumBetLottery.台湾宾果1);
                await DbHelper.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}宾果1{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");

                // 宾果2玩法：取第1、倒数第2、倒数第1个号码进行番摊计算
                result = await RobotHelper.GetDrawResult(lastKj, EnumBetLottery.台湾宾果2);
                await DbHelper.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}宾果2{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");

                // 宾果3玩法：所有号码总和进行番摊计算
                result = await RobotHelper.GetDrawResult(lastKj, EnumBetLottery.台湾宾果3);
                await DbHelper.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}宾果3{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");
            }
            // 一六八飞艇彩种处理
            else if (CommonHelper.Lottery.Equals(EnumLottery.一六八飞艇))
            {
                // 飞艇前3玩法：取第1、2、3位号码进行番摊计算
                int result = await RobotHelper.GetDrawResult(lastKj, EnumBetLottery.一六八飞艇前3);
                await DbHelper.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}飞艇前3{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");

                // 飞艇中3玩法：取第4、5、6位号码进行番摊计算
                result = await RobotHelper.GetDrawResult(lastKj, EnumBetLottery.一六八飞艇中3);
                await DbHelper.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}飞艇中3{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");

                // 飞艇后3玩法：取第8、9、10位号码进行番摊计算
                result = await RobotHelper.GetDrawResult(lastKj, EnumBetLottery.一六八飞艇后3);
                await DbHelper.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}飞艇后3{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");
            }

            // 第七步：生成开奖相关图片
            // 这些图片用于群发和用户查看，提供直观的开奖信息展示

            // 生成开奖数据图：显示最新开奖号码、结果等基础信息
            await ImageHelper.DrawOpenDataAsync(lastKj, ImageHelper.DrawImagePath);

            // 生成7行路子图：显示最近的开奖走势，帮助用户分析规律
            await ImageHelper.DrawTanImageAsync(lastKj, 7, ImageHelper.TanRows7ImagePath);

            // 生成6行路子图：另一种行数的路子图展示
            await ImageHelper.DrawTanImageAsync(lastKj, 6, ImageHelper.TanRows6ImagePath);

            // 生成7行完整路子图：包含更多历史数据的完整路子图
            await ImageHelper.DrawTanImageFullAsync(lastKj, 7, ImageHelper.TanRows77ImagePath);

            // 生成6行完整路子图：另一种行数的完整路子图
            await ImageHelper.DrawTanImageFullAsync(lastKj, 6, ImageHelper.TanRows66ImagePath);

            return true;
        }

        return false;
    }
}