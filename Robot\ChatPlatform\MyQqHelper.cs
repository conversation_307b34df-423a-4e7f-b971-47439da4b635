﻿using AiHelper;
using Flurl.Http;
using Newtonsoft.Json.Linq;
using Robot.Enum;
using Robot.Helper;

namespace Robot.ChatPlatform;

/// <summary>
/// MyQqHelper
/// </summary>
public static class MyQqHelper
{
    private static string Host { get; set; } = @"http://127.0.0.1:8899/MyQQHTTPAPI";

    #region 获取Robot信息

    /// <summary>
    /// 获取Robot信息
    /// </summary>
    public static async Task GetRobotInfo()
    {
        try
        {
            // 构建Json对象
            JObject json = new JObject
            {
                ["function"] = "Api_GetOnlineQQlist",
                ["token"] = "666"
            };

            string response = await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString())
                .ReceiveString();
            if (!string.IsNullOrWhiteSpace(response) && response.Contains("成功"))
            {
                JObject jObj = JObject.Parse(response);
                RobotHelper.RobotInfo.Account = jObj["data"]?["ret"]?.ToString()!;
                RobotHelper.RobotInfo.NickName = await GetNick(RobotHelper.RobotInfo.Account);
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "GetRobotInfo", ex.ToString());
        }
    }

    #endregion

    #region 获取所有群

    /// <summary>
    /// 获取所有群
    /// </summary>
    public static async Task GetGroup()
    {
        try
        {
            // 构建Json对象
            JObject json = new JObject
            {
                ["function"] = "Api_GetGroupList_A",
                ["token"] = "666",
                ["params"] = new JObject
                {
                    ["c1"] = RobotHelper.RobotInfo.Account
                }
            };

            string response = await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString())
                .ReceiveString();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("成功"))
            {
                JObject jObj = JObject.Parse(response);
                string resultData = jObj["data"]?["ret"]?.ToString()!;
                string[] groupIdLines = Ai.Split(resultData, "\r\n");
                foreach (string groupId in groupIdLines)
                {
                    string groupName = await GetGroupName(RobotHelper.RobotInfo.Account, groupId);
                    RobotHelper.GroupDic.TryAdd(groupId, groupName);
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "GetGroup错误", ex.ToString());
        }
    }

    private static async Task<string> GetGroupName(string robotId, string groupId)
    {
        string groupName = "";
        try
        {
            // 构建Json对象
            JObject json = new JObject
            {
                ["function"] = "Api_GetGroupName",
                ["token"] = "666",
                ["params"] = new JObject
                {
                    ["c1"] = $"{robotId}",
                    ["c2"] = $"{groupId}"
                }
            };

            string url = $"{Host}";
            string response = await url
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString())
                .ReceiveString();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("成功"))
            {
                JObject jObj = JObject.Parse(response);
                string resultData = jObj["data"]?["ret"]?.ToString()!;
                groupName = resultData;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "GetGroup错误", ex.ToString());
        }

        return groupName;
    }

    #endregion

    #region 获取昵称

    /// <summary>
    /// 获取昵称
    /// </summary>
    /// <param name="qqNum"></param>
    /// <returns></returns>
    public static async Task<string> GetNick(string qqNum)
    {
        string groupName = "";
        try
        {
            // 构建Json对象
            JObject json = new JObject
            {
                ["function"] = "Api_GetNick",
                ["token"] = "666",
                ["params"] = new JObject
                {
                    ["c1"] = $"{RobotHelper.RobotInfo.Account}",
                    ["c2"] = $"{qqNum}"
                }
            };

            string response = await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString())
                .ReceiveString();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("成功"))
            {
                JObject jObj = JObject.Parse(response);
                string resultData = jObj["data"]?["ret"]?.ToString()!;
                groupName = resultData;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "GetGroup错误", ex.ToString());
        }

        return groupName;
    }

    #endregion

    #region 发送群信息

    /// <summary>
    /// 发送群信息
    /// </summary>
    /// <param name="content"></param>
    public static async Task SendGroupMessage(string content)
    {
        try
        {
            // 构建Json对象
            JObject json = new JObject
            {
                ["function"] = "Api_SendMsg",
                ["token"] = "666",
                ["params"] = new JObject
                {
                    ["c1"] = RobotHelper.RobotInfo.Account,
                    ["c2"] = "2",
                    ["c3"] = RobotHelper.WorkGroupId,
                    ["c4"] = "",
                    ["c5"] = content
                }
            };
            await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString());
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, ex.ToString());
        }
    }

    #endregion

    #region 发送群信息并且@某人

    /// <summary>
    /// 发送群信息并且@某人
    /// </summary>
    /// <param name="content"></param>
    /// <param name="atAccount"></param>
    public static async void SendGroupMessage(string content, string atAccount)
    {
        try
        {
            // 构建Json对象
            JObject json = new JObject
            {
                ["function"] = "Api_SendMsg",
                ["token"] = "666",
                ["params"] = new JObject
                {
                    ["c1"] = RobotHelper.RobotInfo.Account,
                    ["c2"] = "2",
                    ["c3"] = RobotHelper.WorkGroupId,
                    ["c4"] = "",
                    ["c5"] = $"[@{atAccount}]" + content
                }
            };

            await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString());
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "SendGroupMessage", ex.ToString());
        }
    }

    #endregion

    #region 发送图片

    /// <summary>
    /// 发送图片
    /// </summary>
    public static async Task SendImage(string groupId, string imgPath)
    {
        try
        {
            // 构建Json对象
            JObject json = new JObject
            {
                ["function"] = "Api_SendMsg",
                ["token"] = "666",
                ["params"] = new JObject
                {
                    ["c1"] = RobotHelper.RobotInfo.Account,
                    ["c2"] = "2",
                    ["c3"] = groupId,
                    ["c4"] = "",
                    ["c5"] = $"[pic={imgPath}]"
                }
            };

            await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString());
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, ex.ToString());
        }
    }

    #endregion
}