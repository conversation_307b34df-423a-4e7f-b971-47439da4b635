﻿namespace Robot.Models;

/// <summary>
/// 机器人信息模型类 - 存储当前机器人的基本身份信息
///
/// 功能说明：
/// - 存储机器人在聊天平台上的身份信息
/// - 用于消息发送时的身份标识
/// - 支持多平台的机器人信息管理
/// - 提供机器人状态的基础数据
///
/// 使用场景：
/// - 系统启动时获取机器人信息
/// - 消息发送时标识发送者
/// - 日志记录中标识操作主体
/// - 界面显示当前机器人状态
///
/// 数据来源：
/// - 微信平台：通过微信API获取机器人信息
/// - QQ平台：通过QQ API获取机器人信息
/// - VoceChat：使用固定的管理员信息
/// - 其他平台：根据平台特点获取
///
/// 生命周期：
/// - 系统启动时初始化
/// - 运行期间保持不变
/// - 平台切换时重新获取
///
/// 设计特点：
/// - 简单的POCO类，无数据库映射
/// - 全局单例使用，通过RobotHelper.RobotInfo访问
/// - 跨平台兼容，适应不同聊天平台
///
/// 重要性：
/// - 机器人身份的基础信息
/// - 系统运行的必要数据
/// - 用户交互的身份标识
/// </summary>
public class RobotInfo
{
    /// <summary>
    /// 机器人账号 - 机器人在聊天平台上的唯一标识
    ///
    /// 用途：
    /// - 标识机器人的唯一身份
    /// - 用于消息发送的身份验证
    /// - 日志记录中的操作主体标识
    /// - 系统内部的机器人识别
    ///
    /// 平台差异：
    /// - 微信：微信号或微信ID
    /// - QQ：QQ号码
    /// - VoceChat：固定为"admin"
    /// - 其他平台：根据平台规则
    ///
    /// 获取方式：
    /// - 通过各平台的Helper类获取
    /// - 系统启动时自动初始化
    /// - 确保账号信息的准确性
    ///
    /// 重要性：
    /// - 机器人身份的核心标识
    /// - 消息发送的必要信息
    /// - 系统日志的重要字段
    /// </summary>
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 机器人昵称 - 机器人在聊天平台上的显示名称
    ///
    /// 用途：
    /// - 用户界面中显示的机器人名称
    /// - 消息发送时的显示名称
    /// - 日志记录中的可读标识
    /// - 系统状态显示的友好名称
    ///
    /// 平台差异：
    /// - 微信：微信昵称
    /// - QQ：QQ昵称
    /// - VoceChat：固定为"系统管理员"
    /// - 其他平台：根据平台规则
    ///
    /// 特点：
    /// - 比账号更具可读性
    /// - 便于用户识别和理解
    /// - 可能包含中文或特殊字符
    /// - 在不同平台可能有不同格式
    ///
    /// 获取方式：
    /// - 通过各平台的API获取
    /// - 与账号信息同时获取
    /// - 确保昵称信息的实时性
    ///
    /// 业务价值：
    /// - 提升用户体验的友好标识
    /// - 便于系统管理和监控
    /// - 增强日志的可读性
    /// </summary>
    public string NickName { get; set; } = string.Empty;
}