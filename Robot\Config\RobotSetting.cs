﻿using Sunny.UI;

namespace Robot.Config;

[ConfigFile("Config\\RobotSetting.ini")]
public class RobotSetting : IniConfig<RobotSetting>
{
    // 软件名称,注册码
    [ConfigSection("KingRobot")] public string AppName { get; set; } = string.Empty;
    [ConfigSection("KingRobot")] public int AppType { get; set; }
    [ConfigSection("KingRobot")] public string PlatformHost { get; set; } = string.Empty;
    [ConfigSection("KingRobot")] public string UpdateHost { get; set; } = string.Empty;

    /// <summary>
    /// 设置默认值
    /// </summary>
    public override void SetDefault()
    {
        base.SetDefault();
        AppName = "KingRobot";
        AppType = 0;
        PlatformHost = @"http://127.0.0.1:6000"; // 平台地址,用于获取版本信息,载入窗口时获取一次,点击确定按钮时获取一次
        UpdateHost = @"http://**************"; // 更新地址,用于检查更新,点击检查更新时获取一次
    }
}