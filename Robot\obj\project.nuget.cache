{"version": 2, "dgSpecHash": "o1DipMrI0Pk=", "success": true, "projectFilePath": "F:\\SolutionRobotCopy\\Robot\\Robot.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\autoupdater.net.official\\1.9.2\\autoupdater.net.official.1.9.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\costura.fody\\6.0.0\\costura.fody.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\flurl\\4.0.0\\flurl.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\flurl.http\\4.0.2\\flurl.http.4.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fody\\6.8.2\\fody.6.8.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql\\3.5.211\\freesql.3.5.211.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.dbcontext\\3.5.211\\freesql.dbcontext.3.5.211.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.provider.sqlite\\3.5.211\\freesql.provider.sqlite.3.5.211.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.0\\microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.web.webview2\\1.0.2592.51\\microsoft.web.webview2.1.0.2592.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stub.system.data.sqlite.core.netstandard\\1.0.119\\stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sunnyui\\3.8.7\\sunnyui.3.8.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sunnyui.common\\3.8.7\\sunnyui.common.3.8.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\9.0.7\\system.codedom.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlite.core\\1.0.119\\system.data.sqlite.core.1.0.119.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\9.0.7\\system.management.9.0.7.nupkg.sha512"], "logs": []}