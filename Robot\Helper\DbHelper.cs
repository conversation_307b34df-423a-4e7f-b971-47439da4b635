﻿using FreeSql;
using Robot.Enum;
using Robot.Models;

namespace Robot.Helper;

/// <summary>
/// 数据库帮助类
/// </summary>
public static class DbHelper
{
    public static object DbLock { get; } = new();

    public static IFreeSql FSql { get; } = new FreeSqlBuilder()
        .UseConnectionString(DataType.Sqlite, @"Data Source=Robot.db;Version=3;Pooling=true;Max Pool Size=100;Min Pool Size=5;")
        // .UseConnectionString(DataType.Sqlite, @"Host=127.0.0.1;Port=5432;Username=postgres;Password=*************; Database=postgres;ArrayNullabilityMode=Always;Pooling=true;Minimum Pool Size=1")
        .UseAutoSyncStructure(true) //自动同步实体结构到数据库，FreeSql不会扫描程序集，只有CRUD时才会生成表。
        .Build(); //请务必定义成 Singleton 单例模式

    #region InitTable,主要用于创建表格

    /// <summary>
    /// InitTable,主要用于创建表格
    /// </summary>
    public static async void InitTableAsync()
    {
        try
        {
            await FSql.Select<Member>().AnyAsync();
            await FSql.Select<BetOrder>().AnyAsync();
            await FSql.Select<HuiZong>().AnyAsync();

            await FSql.Select<AddMoney>().AnyAsync();
            await FSql.Select<SubMoney>().AnyAsync();
            await FSql.Select<Finance>().AnyAsync();

            await FSql.Select<ReceiveMessage>().AnyAsync();
            await FSql.Select<Log>().AnyAsync();

            await FSql.Select<KaiJiang>().AnyAsync();
        }
        catch (Exception ex)
        {
            await AddLogAsync(EnumLogType.机器人, "InitTable", ex.ToString());
        }
    }

    #endregion
    
    #region 根据Account返回Member

    /// <summary>
    /// 根据Account返回Member
    /// </summary>
    /// <returns></returns>
    public static async Task<Member> GetMemberAsync(string account)
    {
        return await FSql.Select<Member>()
            .Where(a => a.Account.Equals(account))
            .ToOneAsync();
    }

    #endregion

    #region 添加日志

    /// <summary>
    /// 添加日志
    /// </summary>
    /// <param name="type"></param>
    /// <param name="title"></param>
    /// <param name="content"></param>
    public static async Task AddLogAsync(EnumLogType type, string title, string? content = "")
    {
        try
        {
            if (string.IsNullOrWhiteSpace(content))
            {
                content = "null";
            }

            Log log = new Log
            {
                Type = type,
                Title = title,
                Content = content
            };

            log.Id = await FSql.Insert<Log>().AppendData(log).ExecuteIdentityAsync();
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("AddLogError.log", ex + Environment.NewLine + Environment.NewLine);
        }
    }

    #endregion
}