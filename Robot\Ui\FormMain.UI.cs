﻿using System.Diagnostics;
using System.Globalization;
using AiHelper;
using FreeSql;
using Robot.ChatPlatform;
using Robot.Config;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;
using Sunny.UI;

namespace Robot.Ui;

/// <summary>
/// UI事件
/// </summary>
public partial class FormMain
{
    #region 设置对冲吃单状态

    /// <summary>
    /// 是否开启对冲吃单
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_IsDuiChong_CheckedChanged(object? sender, EventArgs e)
    {
        try
        {
            checkBox_IsDuiChong.ForeColor = checkBox_IsDuiChong.Checked ? Color.Red : Color.Black;
            UserSetting.Current.是否对冲吃单 = checkBox_IsDuiChong.Checked;
            UserSetting.Current.Save();
            await DbHelper.AddLogAsync(EnumLogType.机器人, "设置对冲吃单状态", $"{UserSetting.Current.是否对冲吃单}");
            this.ShowSuccessTip(UserSetting.Current.是否对冲吃单 ? "已开启对冲吃单" : "已关闭对冲吃单");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "checkBox_IsDuiChong_CheckedChangedError", ex.ToString());
        }
    }

    #endregion

    private async void button_StartService_Click(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue))
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "拦截开始服务", "飞单平台未登录.");
                UIMessageBox.ShowMessageDialog("请先登录飞单平台后再开始考试!", "操作提醒:", false, UIStyle.Red, true);
                return;
            }

            button_StartService.Enabled = false;

            // 判断是否已经指定考试群
            if (comboBox_WorkGroupId.SelectedIndex <= 0)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "拦截开始服务", "未指定考试群");
                UIMessageBox.ShowMessageDialog("请先指定考试群再开始考试!", "操作提醒:", false, UIStyle.Red, true);
                button_StartService.Enabled = true;
                return;
            }

            // 判断是否已经链接通讯App
            if (!RobotHelper.GroupDic.Any())
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "拦截开始服务", "未指定考试群");
                UIMessageBox.ShowMessageDialog("请先登录通讯App后再开始考试!", "操作提醒:", false, UIStyle.Red, true);
                return;
            }

            // 设置机器人参数
            RobotHelper.WorkGroupId = Ai.GetTextMiddle(comboBox_WorkGroupId.Text.Trim(), Ai.中括号左, Ai.中括号右);
            SoundHelper.MediaStartService.Play();

            // 判断是否发送开奖数据图
            if (UserSetting.Current.是否发送开奖图)
            {
                await RobotHelper.发送开奖图Handler();
            }

            // 判断是否发送开奖路子图
            if (UserSetting.Current.是否发送路子图)
            {
                await Task.Delay(500);
                await RobotHelper.发送路子图Handler();
            }

            // // 发送数据注意提醒
            // if (CommonHelper.Lottery.Equals(EnumLottery.台湾宾果2) && DateTime.Now < new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 7, 5, 0))
            // {
            //     await ChatHelper.SendGroupMessage($"【请注意】以上是昨天最后一期的开奖结果！");
            // }

            // 发送开始服务提示
            await Task.Delay(1000);
            await ChatHelper.SendGroupMessage("【开始服务】" + "\r" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            Thread.Sleep(1000);

            // 发送当前时间
            await RobotHelper.查询时间指令Handler(null);
            await ChatHelper.SendGroupMessage("【请等待开始答题】");

            // 发送开盘提醒
            if (RobotHelper.OpenTipsList.Contains(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue))
            {
                await Task.Delay(500);
                await RobotHelper.发送开盘提醒(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue);
            }

            // 记录进数据库
            await DbHelper.AddLogAsync(EnumLogType.机器人, "启动服务", "当前考试群为:" + Ai.中括号左 + RobotHelper.WorkGroupId + Ai.中括号右);
            comboBox_WorkGroupId.Enabled = false;

            // 开始服务
            CommonHelper.ServiceParamDic[CommonHelper.IsStartService] = true;
            button_StartService.Enabled = false;
            button_StopService.Enabled = true;
            button_StartBet.Enabled = true;
            button_StopBet.Enabled = false;
            CommonHelper.StartServiceTime = DateTime.Now;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "button_Service_ClickError", ex.ToString());
        }
    }

    private async void button_StopService_Click(object sender, EventArgs e)
    {
        try
        {
            // 发送停止服务提示
            await ChatHelper.SendGroupMessage("【停止服务】" + "\r" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            // 重置服务参数
            CommonHelper.StartServiceTime = new DateTime(1970, 1, 1, 8, 0, 0);
            CommonHelper.ServiceParamDic[CommonHelper.IsStartService] = false;
            SoundHelper.MediaStopService.Play();
            comboBox_WorkGroupId.Enabled = true;
            button_StartService.Enabled = true;
            button_StopService.Enabled = false;
            button_StartBet.Enabled = false;
            button_StopBet.Enabled = false;

            UserSetting.Current.是否开启飞单 = false;
            UserSetting.Current.Save();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "button_StopService_ClickError", ex.ToString());
        }
    }

    private async void button_StartBet_Click(object sender, EventArgs e)
    {
        try
        {
            UserSetting.Current.是否开启飞单 = true;
            UserSetting.Current.Save();
            await DbHelper.AddLogAsync(EnumLogType.机器人, "设置飞单状态", UserSetting.Current.是否开启飞单 ? "开启" : "关闭");
            this.ShowSuccessTip(UserSetting.Current.是否开启飞单 ? "已开启飞单" : "已关闭飞单");

            button_StartBet.Enabled = false;
            button_StopBet.Enabled = true;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "checkBox_IsFeiDan_CheckedChangedError", ex.ToString());
        }
    }

    private async void button_StopBet_Click(object sender, EventArgs e)
    {
        try
        {
            UserSetting.Current.是否开启飞单 = false;
            UserSetting.Current.Save();
            await DbHelper.AddLogAsync(EnumLogType.机器人, "设置飞单状态", UserSetting.Current.是否开启飞单 ? "开启" : "关闭");
            this.ShowSuccessTip(UserSetting.Current.是否开启飞单 ? "已开启飞单" : "已关闭飞单");

            button_StartBet.Enabled = true;
            button_StopBet.Enabled = false;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "checkBox_IsFeiDan_CheckedChangedError", ex.ToString());
        }
    }


    #region 飞单失败一键退回

    /// <summary>
    /// 飞单失败一键退回
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void uiButton_CancelBetOrder_Click(object sender, EventArgs e)
    {
        try
        {
            // 先判断是否开启自动退单
            if (UserSetting.Current.AutoCancelOrder)
            {
                UIMessageBox.ShowWarning(@"你已开启自动退单,请等待系统自动操作!", true);
                return;
            }

            // 弹出确认对话框
            if (!UIMessageBox.ShowMessageDialog(@"确定要撤销指定期号的投注数据吗?该操作非常危险且不可逆,撤销后将无法恢复,请谨慎操作!确定撤销请按确定按钮,取消请按取消按钮？", @"确认操作", true, UIStyle.Red))
            {
                return;
            }

            // 记录日志
            await DbHelper.AddLogAsync(EnumLogType.机器人, "手动撤销订单", "开始手动退回.");
            await BetHelper.AutoCancelBetFailedData();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "CancelBetOrderError", ex.ToString());
        }
    }

    #endregion

    #region 卡奖时手动开盘

    /// <summary>
    /// 卡奖时手动开盘
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void uiButton_HandToOpen_Click(object sender, EventArgs e)
    {
        try
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "操作手动开盘", "操作手动开盘.");

            // 提取出当前期号
            string issueNow = IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue;

            // 先判断是否为卡奖时段
            if (!RobotHelper.OpenTipsList.Contains(issueNow))
            {
                RobotHelper.OpenTipsList.Add(issueNow);
                await RobotHelper.发送开盘提醒(issueNow);
                await DbHelper.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "开始接受答题" + Ai.中括号右, string.Concat(Ai.中括号左, issueNow, Ai.中括号右, $"期开始接受答题\r{Ai.中括号左}本期为卡奖时段管理员手动开始{Ai.中括号右}"));

                // 发送开盘提示音
                SoundHelper.MediaOpenDraw.Play();
                UIMessageBox.ShowSuccess($@"{Ai.中括号左}{issueNow}{Ai.中括号右}期已手动开盘,请留意群内消息.");
            }
            else
            {
                UIMessageBox.ShowWarning($@"{Ai.中括号左}{issueNow}{Ai.中括号右}期已在开盘状态,无需再次手动开盘.");
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "uiButton_HandToOpen_ClickError", ex.ToString());
        }
    }

    #endregion

    #region 菜单栏切换事件

    /// <summary>
    /// 菜单栏切换事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void tabControl_Main_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            if (tabControl_Main.SelectedTab == tabPage_Home)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "操作记录", "打开主页");
            }
            else if (tabControl_Main.SelectedTab == tabPage_BetOrderReport)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "操作记录", "打开订单记录");
                // 添加会员下拉框
                await AddMemberToComboBox(comboBox_SelectMemberForBetOrder);

                // 添加期号下拉框
                List<string> tmpIssueList = DbHelper.FSql.Select<BetOrder>().ToList(a => a.Issue);
                List<string> issueList = tmpIssueList.Distinct().OrderBy(a => a).ToList();
                comboBox_SelectIssueForBetOrder.Items.Clear();
                comboBox_SelectIssueForBetOrder.Items.Add("全部");
                foreach (string issue in issueList)
                {
                    comboBox_SelectIssueForBetOrder.Items.Add(issue);
                }
            }
            else if (tabControl_Main.SelectedTab == tabPage_BetTotalReport)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "操作记录", "打开飞单汇总");
                // 添加期号下拉框
                List<string> issueList = DbHelper.FSql.Select<HuiZong>().ToList(t => t.Issue);
                issueList.Reverse();
                issueList = issueList.Distinct().ToList();
                comboBox_SelectIssueForHuiZong.Items.Clear();
                comboBox_SelectIssueForHuiZong.Items.Add("全部");
                comboBox_SelectIssueForHuiZong.Items.Add("成功");
                comboBox_SelectIssueForHuiZong.Items.Add("失败");
                foreach (string issue in issueList)
                {
                    comboBox_SelectIssueForHuiZong.Items.Add(issue);
                }

                comboBox_SelectIssueForHuiZong.SelectedIndex = 0;
            }
            else if (tabControl_Main.SelectedTab == tabPage_AddMoneyReport)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "操作记录", "打开上分记录");
                await AddMemberToComboBox(comboBox_SelectObjForAddMoneyReport);
            }
            else if (tabControl_Main.SelectedTab == tabPage_SubMoneyReport)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "操作记录", "打开下分记录");
                await AddMemberToComboBox(comboBox_SelectObjForSubMoneyReport);
            }
            else if (tabControl_Main.SelectedTab == tabPage_FinanceReport)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "操作记录", "打开财务记录");
                await AddMemberToComboBox(comboBox_SelectObjForFinanceReport);
            }
            else if (tabControl_Main.SelectedTab == tabPage_RecMsg)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "操作记录", "打开消息记录");
                await AddMemberToComboBox(comboBox_SelectMemberForShowRecMsg);
            }
            else if (tabControl_Main.SelectedTab == tabPage_Setting)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "操作记录", "打开系统设置");
            }
            else if (tabControl_Main.SelectedTab == tabPage_Log)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "操作记录", "打开日志页面");
                await Task.Run(ShowLog);
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "tabControl_Main_SelectedIndexChangedError", ex.ToString());
        }
    }

    private async Task AddMemberToComboBox(ComboBox comboBox)
    {
        try
        {
            List<Member> memberList = await DbHelper.FSql.Select<Member>().ToListAsync();
            comboBox.Items.Clear();
            comboBox.Items.Add("全部");
            foreach (Member member in memberList)
            {
                comboBox.Items.Add($"{Ai.中括号左}{member.Account}{Ai.中括号右}{member.昵称}");
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "AddMemberToComboBoxError", ex.ToString());
        }
    }

    #endregion

    #region 系统设置

    /// <summary>
    /// 打开赔率管理窗口
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void uiButton_Odds_Click(object sender, EventArgs e)
    {
        FormOdds frm = new FormOdds();
        frm.ShowDialog();
    }

    /// <summary>
    /// 设置封盘时间
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void comboBox_CloseTime_SelectedIndexChanged(object? sender, EventArgs e)
    {
        try
        {
            UserSetting.Current.封盘时间 = Convert.ToInt32(comboBox_CloseTime.Text.Trim());
            UserSetting.Current.Save();
            await DbHelper.AddLogAsync(EnumLogType.机器人, "设置封盘时间", $"{UserSetting.Current.封盘时间}");
            // this.ShowSuccessTip($@"封盘时间已设置为{Ai.中括号左}{Setting.Current.封盘时间}{Ai.中括号右}秒");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "comboBox_CloseTime_SelectedIndexChangedError", ex.ToString());
        }
    }

    /// <summary>
    /// 保存回水比例
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void uiButton_SaveReturnCommissionPercent_Click(object sender, EventArgs e)
    {
        try
        {
            // 取出输入值
            decimal percent = decimal.Parse(textBox_ReturnCommissionPercent.Text.Trim(), CultureInfo.InvariantCulture);
            if (percent < 0)
            {
                await Task.Run(() => { MessageBox.Show(@"回水比例必须大于或等于0."); });
                return;
            }

            // 比较输入值
            if (percent.Equals(UserSetting.Current.回水比例))
            {
                // await Task.Run(() => { MessageBox.Show(@"输入的值与原有的值相同."); });
                this.ShowErrorDialog(@"操作提醒:", @"输入的值与原有的值相同.");
                return;
            }

            // 更新设置
            decimal oldReturnCommissionPercent = UserSetting.Current.回水比例;
            UserSetting.Current.回水比例 = Convert.ToDecimal(textBox_ReturnCommissionPercent.Text.Trim());
            UserSetting.Current.Save();

            // 更新数据库
            await DbHelper.FSql.Update<Member>()
                .Set(a => a.回水比例, UserSetting.Current.回水比例)
                .Where(a => a.回水比例.Equals(oldReturnCommissionPercent))
                .ExecuteAffrowsAsync();

            CommonHelper.NeedToRefreshMemberInfo = true;
            await DbHelper.AddLogAsync(EnumLogType.机器人, "设置回水比例", UserSetting.Current.回水比例.ToString(CultureInfo.InvariantCulture));
            // await Task.Run(() => { MessageBox.Show(@"设置回水比例成功."); });
            this.ShowSuccessDialog($@"设置回水比例成功,原有值{oldReturnCommissionPercent}%,新值{UserSetting.Current.回水比例}%.");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "SaveReturnCommissionPercentError", ex.ToString());
        }
    }

    /// <summary>
    /// 一键返水
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void uiButton_OneKeyRebate_Click(object sender, EventArgs e)
    {
        try
        {
            await FinanceHelper.OneKeyRebateAsync();
            this.ShowSuccessDialog(@"一键返水完毕.");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "OneKeyRebateError", ex.ToString());
        }
    }

    /// <summary>
    /// 7行路子图
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_7Rows_CheckedChanged(object? sender, EventArgs e)
    {
        try
        {
            UserSetting.Current.SendImageRows7 = checkBox_7Rows.Checked;
            UserSetting.Current.Save();
            checkBox_7Rows.ForeColor = checkBox_7Rows.Checked ? Color.Red : Color.Black;
            await DbHelper.AddLogAsync(EnumLogType.机器人, "设置发送路子图7行", UserSetting.Current.SendImageRows7 ? "开启" : "关闭");
            // this.ShowSuccessTip(Setting.Current.SendImageRows7 ? "已开启7行路子图" : "已关闭7行路子图");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "checkBox_7Rows_CheckedChangedError", ex.ToString());
        }
    }

    /// <summary>
    ///  6行路子图
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_6Rows_CheckedChanged(object? sender, EventArgs e)
    {
        try
        {
            UserSetting.Current.SendImageRows6 = checkBox_6Rows.Checked;
            UserSetting.Current.Save();
            checkBox_6Rows.ForeColor = checkBox_6Rows.Checked ? Color.Red : Color.Black;
            await DbHelper.AddLogAsync(EnumLogType.机器人, "设置发送路子图6行", UserSetting.Current.SendImageRows6 ? "开启" : "关闭");
            // this.ShowSuccessTip(Setting.Current.SendImageRows6 ? "已开启6行路子图" : "已关闭6行路子图");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "checkBox_6Rows_CheckedChangedError", ex.ToString());
        }
    }

    /// <summary>
    /// 摊路图模式1
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void radioButton_ImgType1_CheckedChanged(object sender, EventArgs e)
    {
        UserSetting.Current.ImgType = 1;
        UserSetting.Current.Save();
    }

    /// <summary>
    /// 摊路图模式2
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void radioButton_ImgType2_CheckedChanged(object sender, EventArgs e)
    {
        UserSetting.Current.ImgType = 2;
        UserSetting.Current.Save();
    }

    /// <summary>
    /// 自动回水
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_AutoHuiShui_CheckedChanged(object? sender, EventArgs e)
    {
        try
        {
            checkBox_AutoHuiShui.ForeColor = checkBox_AutoHuiShui.Checked ? Color.Red : Color.Black;
            UserSetting.Current.自动回水 = checkBox_AutoHuiShui.Checked;
            UserSetting.Current.Save();
            await DbHelper.AddLogAsync(EnumLogType.机器人, "设置自动回水状态", $"{UserSetting.Current.自动回水}");
            this.ShowSuccessTip(UserSetting.Current.自动回水 ? "已开启自动回水" : "已关闭自动回水");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "checkBox_AutoHuiShui_CheckedChangedError", ex.ToString());
        }
    }

    /// <summary>
    /// 假人自动上分
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_JiaRenAutoAddMoney_CheckedChanged(object? sender, EventArgs e)
    {
        try
        {
            checkBox_JiaRenAutoAddMoney.ForeColor = checkBox_JiaRenAutoAddMoney.Checked ? Color.Red : Color.Black;
            UserSetting.Current.假人自动上分 = checkBox_JiaRenAutoAddMoney.Checked;
            UserSetting.Current.Save();
            await DbHelper.AddLogAsync(EnumLogType.机器人, "设置假人自动上分状态", $"{UserSetting.Current.假人自动上分}");
            this.ShowSuccessTip(UserSetting.Current.假人自动上分 ? "已开启假人自动上分" : "已关闭假人自动上分");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "checkBox_JiaRenAutoAddMoney_CheckedChangedError", ex.ToString());
        }
    }

    /// <summary>
    /// 撤销指定期号投注数据
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void uiButton_CancelBetData_Click(object sender, EventArgs e)
    {
        try
        {
            string issue = textBox_CancelIssue.Text.Trim();
            if (string.IsNullOrWhiteSpace(issue))
            {
                this.ShowErrorDialog("操作提醒:", "请输入需要撤销的期号.");
                return;
            }

            // 弹出确认对话框
            if (!UIMessageBox.ShowMessageDialog(@"确定要撤销指定期号的投注数据吗?该操作非常危险且不可逆,撤销后将无法恢复,请谨慎操作!", @"操作提醒:", true, UIStyle.Red))
            {
                return;
            }

            // 提取出指定期号的投注数据
            List<BetOrder> toBetOrderList = await DbHelper.FSql.Select<BetOrder>().Where(a => a.Issue.Equals(issue)).ToListAsync();

            // 提取出投注彩种
            List<EnumBetLottery> betLotteryList = toBetOrderList.Select(a => a.BetLottery).Distinct().ToList();

            // 遍历彩种,撤单
            foreach (EnumBetLottery betLottery in betLotteryList)
            {
                // 取出该彩种的所有投注数据
                List<BetOrder> cancelBetOrderList = toBetOrderList.Where(a => a.BetLottery == betLottery).ToList();

                // 调用撤单方法
                await BetHelper.CancelBetData(cancelBetOrderList);
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "CancelBetDataError", ex.ToString());
        }
    }

    /// <summary>
    /// 保留会员备注名
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void checkBox_SaveMemberRemarkName_CheckedChanged(object? sender, EventArgs e)
    {
        checkBox_SaveMemberBaseInfo.ForeColor = checkBox_SaveMemberBaseInfo.Checked ? Color.Red : Color.Black;
    }

    /// <summary>
    /// 保存会员余额
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void checkBox_SaveMemberBalance_CheckedChanged(object? sender, EventArgs e)
    {
        checkBox_SaveMemberBalance.ForeColor = checkBox_SaveMemberBalance.Checked ? Color.Red : Color.Black;
    }

    /// <summary>
    /// 清空数据按钮点击事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void uiButton_Clear_Click(object sender, EventArgs e)
    {
        try
        {
            // 弹出确认对话框
            if (!UIMessageBox.ShowMessageDialog(@"删除数据无法恢复，请再次确认真的要清空数据吗？", @"确认操作", true, UIStyle.Red))
            {
                return;
            }

            await RobotHelper.SendMemberInfoAsync();
            if (checkBox_SaveMemberBalance.Checked)
            {
                // 保留会员Id,备注名,回水比例,余额
            }
            else if (checkBox_SaveMemberBaseInfo.Checked)
            {
                // 只保留会员Id,备注名,回水比例
                await DbHelper.FSql.Update<Member>()
                    .Set(a => a.Balance, 0)
                    .Where(a => !string.IsNullOrWhiteSpace(a.Account))
                    .ExecuteAffrowsAsync();
            }
            else
            {
                // 删除所有数据
                await DbHelper.FSql.Delete<Member>()
                    .Where(a => !string.IsNullOrWhiteSpace(a.Account))
                    .ExecuteAffrowsAsync();
            }

            await DbHelper.FSql.Delete<AddMoney>().Where("1=1").ExecuteAffrowsAsync();
            await DbHelper.FSql.Delete<SubMoney>().Where("1=1").ExecuteAffrowsAsync();
            await DbHelper.FSql.Delete<Finance>().Where("1=1").ExecuteAffrowsAsync();
            await DbHelper.FSql.Delete<BetOrder>().Where("1=1").ExecuteAffrowsAsync();
            await DbHelper.FSql.Delete<HuiZong>().Where("1=1").ExecuteAffrowsAsync();
            await DbHelper.FSql.Delete<LaShouRebate>().Where("1=1").ExecuteAffrowsAsync();
            await DbHelper.FSql.Delete<ReceiveMessage>().Where("1=1").ExecuteAffrowsAsync();
            await DbHelper.FSql.Delete<SettleReport>().Where("1=1").ExecuteAffrowsAsync();
            await DbHelper.FSql.Delete<Log>().Where("1=1").ExecuteAffrowsAsync();
            // await DbHelper.FSql.Insert<Log>().AppendData(new Log { Type = EnumLogType.机器人, Title = "清空数据", Content = "清空数据成功" }).ExecuteAffrowsAsync();

            // 设置刷新表格 
            CommonHelper.NeedToRefreshMemberInfo = true;
            this.ShowSuccessTip("清空数据成功");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "ClearData_Error", ex.ToString());
            //await Task.Run(() => { MessageBox.Show(@"清空数据失败", ex.ToString()); });
            this.ShowErrorDialog(@"清空数据失败.", ex.ToString());
        }
    }

    /// <summary>
    /// 打开标准时间网站
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void uiButton_ShowTime_Click(object sender, EventArgs e)
    {
        // 使用默认浏览器打开时间戳网站
        try
        {
            // Process.Start("https://biaozhunshijian.bmcx.com/");
            // Process.Start("https://bjtime.org.cn/");
            string url = "https://bjtime.org.cn/"; // 指定要打开的网址
            Process.Start(new ProcessStartInfo(url) { UseShellExecute = true });
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "打开时间戳网站失败", ex.ToString());
        }
    }

    /// <summary>
    /// 设置是否显示回水金额
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void checkBox_ShowReturnCommissionDetail_CheckedChanged(object sender, EventArgs e)
    {
        UserSetting.Current.显示回水金额 = checkBox_ShowReturnCommissionDetail.Checked ? 1 : 0;
        UserSetting.Current.Save();
        checkBox_ShowReturnCommissionDetail.ForeColor = checkBox_ShowReturnCommissionDetail.Checked ? Color.Red : Color.Black;
    }

    #endregion

    #region 会员信息表格右键菜单

    /// <summary>
    /// 弹出右键菜单
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void dataGridView_MemberInfo_CellMouseDown(object? sender, DataGridViewCellMouseEventArgs e)
    {
        try
        {
            if (e is { Button: MouseButtons.Right, RowIndex: >= 0 })
            {
                // 取消之前选中的行
                dataGridView_MemberInfo.ClearSelection();

                // 选中右键点击的行,取出对应信息
                dataGridView_MemberInfo.Rows[e.RowIndex].Selected = true;
                string account = dataGridView_MemberInfo.Rows[e.RowIndex].Cells[1].Value.ToString()!;
                CommonHelper.CurrentMember = await DbHelper.GetMemberAsync(account);

                // 设置菜单选项
                contextMenuStrip_Menu.Items.Clear();
                contextMenuStrip_Menu.Items.Add(@"刷新数据");
                contextMenuStrip_Menu.Items.Add($@"账号:{CommonHelper.CurrentMember.Account}");
                contextMenuStrip_Menu.Items.Add($@"昵称:{CommonHelper.CurrentMember.昵称}");
                contextMenuStrip_Menu.Items.Add($@"备注名:{CommonHelper.CurrentMember.备注名}");
                contextMenuStrip_Menu.Items.Add($@"总上分:{await DbHelper.FSql.Select<AddMoney>().Where(a => a.Account == CommonHelper.CurrentMember.Account && a.Status == EnumBalanceStatus.同意).SumAsync(a => a.Money)}");
                contextMenuStrip_Menu.Items.Add($@"总下分:{await DbHelper.FSql.Select<SubMoney>().Where(a => a.Account == CommonHelper.CurrentMember.Account && a.Status == EnumBalanceStatus.同意).SumAsync(a => a.Money)}");
                contextMenuStrip_Menu.Items.Add("---------------------------");
                contextMenuStrip_Menu.Items.Add("1.操作上分", null, toolStripMenuItem_Click);
                contextMenuStrip_Menu.Items.Add("2.操作下分", null, toolStripMenuItem_Click);
                contextMenuStrip_Menu.Items.Add("3.修改回水比例", null, toolStripMenuItem_Click);
                contextMenuStrip_Menu.Items.Add("4.修改备注名", null, toolStripMenuItem_Click);
                contextMenuStrip_Menu.Items.Add(CommonHelper.CurrentMember.是否假人 ? "5.把他设为真人" : "5.把他设为假人", null, toolStripMenuItem_Click);
                contextMenuStrip_Menu.Items.Add("6.设置拉手上级", null, toolStripMenuItem_Click);

                // 显示ContextMenuStrip
                Point mousePosition = dataGridView_MemberInfo.PointToClient(Cursor.Position);
                contextMenuStrip_Menu.Show(dataGridView_MemberInfo, mousePosition);
            }
        }
        catch (Exception ex)
        {
            // Debug.WriteLine(ex.ToString());
            await DbHelper.AddLogAsync(EnumLogType.机器人, "dataGridView_MemberInfo_右键菜单Error", ex.ToString());
        }
    }

    /// <summary>
    /// 右键菜单选项点击事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void toolStripMenuItem_Click(object? sender, EventArgs e)
    {
        try
        {
            ToolStripMenuItem? toolStripMenuItem = sender as ToolStripMenuItem;
            if (toolStripMenuItem == null || toolStripMenuItem.Text == null)
            {
                return;
            }

            switch (toolStripMenuItem.Text)
            {
                case "刷新数据":
                    CommonHelper.NeedToRefreshMemberInfo = true;
                    break;
                case "1.操作上分":
                    await AddMoneyHandler();
                    CommonHelper.NeedToRefreshMemberInfo = true;
                    break;
                case "2.操作下分":
                    await SubMoneyHandler();
                    CommonHelper.NeedToRefreshMemberInfo = true;
                    break;
                case "3.修改回水比例":
                    await ChangeBackwaterRatioHandler();
                    CommonHelper.NeedToRefreshMemberInfo = true;
                    break;
                case "4.修改备注名":
                    await ChangeRemarkNameHandler();
                    CommonHelper.NeedToRefreshMemberInfo = true;
                    break;
                case "5.把他设为假人":
                    try
                    {
                        // 更新数据库标记
                        await DbHelper.FSql.Update<Member>()
                            .Set(a => a.是否假人, true)
                            .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                            .ExecuteAffrowsAsync();

                        // 记录日志
                        await DbHelper.AddLogAsync(EnumLogType.机器人, "操作会员", $"设置{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}为{Ai.中括号左}假人{Ai.中括号右}");
                        CommonHelper.NeedToRefreshMemberInfo = true;
                    }
                    catch (Exception ex)
                    {
                        await DbHelper.AddLogAsync(EnumLogType.机器人, "toolStripMenuItem_ClickError", ex.ToString());
                    }

                    break;
                case "5.把他设为真人":
                    try
                    {
                        // 更新数据库标记
                        await DbHelper.FSql.Update<Member>()
                            .Set(a => a.是否假人, false)
                            .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                            .ExecuteAffrowsAsync();

                        // 记录日志
                        await DbHelper.AddLogAsync(EnumLogType.机器人, "操作会员", $"设置{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}为{Ai.中括号左}真人{Ai.中括号右}");
                        CommonHelper.NeedToRefreshMemberInfo = true;
                    }
                    catch (Exception ex)
                    {
                        await DbHelper.AddLogAsync(EnumLogType.机器人, "toolStripMenuItem_ClickError", ex.ToString());
                    }

                    break;
                case "6.设置拉手上级":
                    FormSetParentAccount input = new FormSetParentAccount();
                    input.ShowDialog();

                    break;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "toolStripMenuItem_ClickError", ex.ToString());
        }
    }

    /// <summary>
    /// 管理员手动上分
    /// </summary>
    private async Task AddMoneyHandler()
    {
        try
        {
            FormInputBox input = new FormInputBox("管理手动上分", "请输入上分值:", "0");
            DialogResult dr = input.ShowDialog();
            if (dr == DialogResult.OK && input.Value.Length > 0)
            {
                decimal inputValue = decimal.Parse(input.Value);
                if (inputValue > 0)
                {
                    // 使用事务操作数据库
                    using DbContext? dbContext = DbHelper.FSql.CreateDbContext();

                    // 创建AddMoney对象
                    AddMoney am = new AddMoney
                    {
                        Account = CommonHelper.CurrentMember.Account,
                        Money = inputValue,
                        FromMsgId = "管理员手动上分",
                        PreTime = DateTime.Now,
                        Status = EnumBalanceStatus.同意
                    };

                    // 保存到数据库
                    await DbHelper.FSql.Insert<AddMoney>()
                        .AppendData(am)
                        .ExecuteIdentityAsync();

                    // 更新会员信息
                    Member tmpMember = await DbHelper.FSql.Select<Member>().Where(a => a.Account == CommonHelper.CurrentMember.Account).FirstAsync();
                    await DbHelper.FSql.Update<Member>()
                        .Set(a => a.Balance, tmpMember.Balance + inputValue)
                        .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                        .ExecuteAffrowsAsync();

                    // 创建财务凭据
                    Finance fn = new Finance
                    {
                        Account = CommonHelper.CurrentMember.Account,
                        变动前 = tmpMember.Balance,
                        变动值 = inputValue,
                        变动后 = tmpMember.Balance + inputValue,
                        凭据 = "管理员手动上分"
                    };

                    // 记录财务凭据
                    await DbHelper.FSql.Insert<Finance>()
                        .AppendData(fn)
                        .ExecuteIdentityAsync();

                    // 提交保存操作
                    await dbContext.SaveChangesAsync();

                    // 发送群消息
                    await ChatHelper.SendGroupMessage($@"上分{Ai.中括号左}{inputValue}{Ai.中括号右}成功" + "\r" + CommonHelper.GetFaceIdMoneyBag() + ":" + Math.Round(tmpMember.Balance + inputValue, 2), CommonHelper.CurrentMember.Account);

                    // 记录日志
                    await DbHelper.AddLogAsync(EnumLogType.机器人, "操作会员", $"管理员手动给{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}上分{Ai.中括号左}{inputValue}{Ai.中括号右}");
                }
                else if (inputValue < 0)
                {
                    Invoke(() => { MessageBox.Show(@"请输入正确的分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
                }
            }

            input.Dispose();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "管理员手动上分Error", ex.ToString());
            Invoke(() => { MessageBox.Show(@"请输入正确的分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
        }
    }

    /// <summary>
    /// 管理员手动下分
    /// </summary>
    private async Task SubMoneyHandler()
    {
        try
        {
            FormInputBox input = new FormInputBox("管理手动下分", "请输入下分值:", "0");
            DialogResult dr = input.ShowDialog();
            if (dr == DialogResult.OK && input.Value.Length > 0)
            {
                decimal inputValue = decimal.Parse(input.Value);
                if (inputValue > 0)
                {
                    // 获取当前会员信息
                    Member tmpMember = await DbHelper.FSql.Select<Member>().Where(a => a.Account == CommonHelper.CurrentMember.Account).FirstAsync();

                    // 输入的分数大于当前余额
                    if (inputValue > tmpMember.Balance)
                    {
                        await Task.Run(() => { MessageBox.Show(@"输入的分数不能超过用户当前可用分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
                        return;
                    }

                    // 使用事务操作数据库
                    using DbContext? dbContext = DbHelper.FSql.CreateDbContext();

                    // 创建SubMoney对象
                    SubMoney sm = new SubMoney
                    {
                        Account = CommonHelper.CurrentMember.Account,
                        Money = inputValue,
                        FromMsgId = "管理员手动下分",
                        PreTime = DateTime.Now,
                        Status = EnumBalanceStatus.同意
                    };

                    // 保存到数据库
                    await DbHelper.FSql.Insert<SubMoney>()
                        .AppendData(sm)
                        .ExecuteIdentityAsync();

                    // 创建财务凭据
                    Finance fn = new Finance
                    {
                        Account = CommonHelper.CurrentMember.Account,
                        变动前 = tmpMember.Balance,
                        变动值 = -inputValue,
                        变动后 = tmpMember.Balance - inputValue,
                        凭据 = "管理员手动下分"
                    };

                    // 记录财务凭据
                    await DbHelper.FSql.Insert<Finance>()
                        .AppendData(fn)
                        .ExecuteIdentityAsync();

                    // 修改会员信息
                    tmpMember.Balance -= inputValue;
                    await DbHelper.FSql.Update<Member>()
                        .Set(a => a.Balance, tmpMember.Balance)
                        .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                        .ExecuteAffrowsAsync();

                    // 提交保存操作
                    await dbContext.SaveChangesAsync();

                    // 发送群消息
                    await ChatHelper.SendGroupMessage($@"下分{Ai.中括号左}{inputValue}{Ai.中括号右}成功" + "\r" + CommonHelper.GetFaceIdMoneyBag() + ":" + Math.Round(tmpMember.Balance, 2), CommonHelper.CurrentMember.Account);

                    // 记录日志
                    await DbHelper.AddLogAsync(EnumLogType.机器人, "操作会员", $"管理员手动给{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}下分{Ai.中括号左}{inputValue}{Ai.中括号右}");
                }
                else if (inputValue < 0)
                {
                    Invoke(() => { MessageBox.Show(@"请输入正确的分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
                }
            }

            input.Dispose();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "管理员手动下分Error", ex.ToString());
            Invoke(() => { MessageBox.Show(@"请输入正确的分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
        }
    }

    /// <summary>
    /// 修改回水比例
    /// </summary>
    private async Task ChangeBackwaterRatioHandler()
    {
        try
        {
            FormInputBox input = new FormInputBox("修改回水比例", "请输入回水比例:", CommonHelper.CurrentMember.回水比例.ToString(CultureInfo.InvariantCulture));
            DialogResult dr = input.ShowDialog();
            if (dr == DialogResult.OK && input.Value.Length > 0)
            {
                decimal value = Convert.ToDecimal(input.Value);

                // 更新数据库
                await DbHelper.FSql.Update<Member>()
                    .Set(a => a.回水比例, value)
                    .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                    .ExecuteAffrowsAsync();

                // 记录日志
                await DbHelper.AddLogAsync(EnumLogType.平台, "操作会员", $"修改{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}的回水比例为{Ai.中括号左}{value}{Ai.中括号右}");
            }

            input.Dispose();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "toolStripMenuItem_Click_修改回水比例Error", ex.ToString());
        }
    }

    /// <summary>
    /// 修改备注名
    /// </summary>
    private async Task ChangeRemarkNameHandler()
    {
        try
        {
            FormInputBox input = new FormInputBox("修改备注名", "请输入备注名:", CommonHelper.CurrentMember.备注名);
            DialogResult dr = input.ShowDialog();
            if (dr == DialogResult.OK && input.Value.Length > 0)
            {
                string value = input.Value;

                // 更新数据库
                await DbHelper.FSql.Update<Member>()
                    .Set(a => a.备注名, value)
                    .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                    .ExecuteAffrowsAsync();

                // 记录日志
                await DbHelper.AddLogAsync(EnumLogType.平台, "操作会员", $"修改{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}的备注名为{Ai.中括号左}{value}{Ai.中括号右}");
            }

            input.Dispose();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "toolStripMenuItem_Click_修改回水比例Error", ex.ToString());
        }
    }

    #endregion
}