﻿using Robot.Enum;
using Robot.Helper;
using Robot.Models;

namespace Robot.Ui;

/// <summary>
/// DataGridView表格样式
/// </summary>
public partial class FormMain
{
    /// <summary>
    /// MemberInfo
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void dataGridView_MemberInfo_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (dataGridView_MemberInfo.Rows.Count <= 0)
            {
                return;
            }

            // 第一行特殊处理,统计数据显示为绿色
            if (e.RowIndex.Equals(0))
            {
                //e.CellStyle!.BackColor = Color.Green;
                //e.CellStyle!.ForeColor = Color.White;
                // e.CellStyle!.Font = new Font(dataGridView_MemberInfo.DefaultCellStyle.Font, FontStyle.Bold);
            }
            else if (e is { RowIndex: >= 0 })
            {
                switch (e.ColumnIndex)
                {
                    case 1:
                        try
                        {
                            string account = e.Value!.ToString()!;
                            Member member = await DbHelper.FSql.Select<Member>()
                                .Where(a => a.Account == account)
                                .ToOneAsync();

                            // 假人显示红色,吃他数据显示橙色
                            if (member.是否假人)
                            {
                                e.CellStyle!.BackColor = Color.Red;
                                e.CellStyle!.ForeColor = Color.White;
                            }
                        }
                        catch (Exception ex)
                        {
                            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowMemberInfoAsyncError", ex.ToString());
                        }

                        break;
                    case 5:
                        try
                        {
                            // 尝试将单元格的值转换为数值类型
                            if (e.Value != null && double.TryParse(e.Value.ToString(), out double cellValue))
                            {
                                // 如果数值小于0，设置字体颜色为红色
                                if (cellValue < 0)
                                {
                                    e.CellStyle!.BackColor = Color.Red;
                                    e.CellStyle!.ForeColor = Color.White;
                                    // e.CellStyle!.Font = new Font(dataGridView_MemberInfo.DefaultCellStyle.Font, FontStyle.Bold);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowMemberInfoAsyncError", ex.ToString());
                        }

                        break;
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowMemberInfoAsyncError", ex.ToString());
        }
    }

    /// <summary>
    /// BetOrderDetail
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void dataGridView_BetOrderReport_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (dataGridView_BetOrderReport.Rows.Count <= 0)
            {
                return;
            }

            if (e is { RowIndex: >= 0, ColumnIndex: 14 })
            {
                try
                {
                    string? cellValue = dataGridView_BetOrderReport.Rows[e.RowIndex].Cells[e.ColumnIndex].Value.ToString();
                    if (cellValue is "假人订单")
                    {
                        e.CellStyle!.BackColor = Color.Red;
                        e.CellStyle!.ForeColor = Color.White;
                        // e.CellStyle!.Font = new Font(dataGridView_MemberInfo.DefaultCellStyle.Font, FontStyle.Bold);
                    }
                }
                catch (Exception ex)
                {
                    await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowMemberInfoAsyncError", ex.ToString());
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowMemberInfoAsyncError", ex.ToString());
        }
    }

    /// <summary>
    /// AddMoney
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void dataGridView_AddMoney_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (e.RowIndex >= 0 && (e.ColumnIndex == 4 || e.ColumnIndex == 5))
            {
                // 设置按钮颜色
                DataGridViewButtonCell? cell = (DataGridViewButtonCell)dataGridView_AddMoney.Rows[e.RowIndex].Cells[e.ColumnIndex];
                cell.FlatStyle = FlatStyle.Flat;
                cell.Style.ForeColor = Color.White;
                switch (e.ColumnIndex)
                {
                    case 4:
                        cell.Style.BackColor = Color.Green;
                        break;
                    case 5:
                        cell.Style.BackColor = Color.Red;
                        break;
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowAddMoneyOrderAsyncError", ex.ToString());
        }
    }

    /// <summary>
    /// SubMoney
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void dataGridView_SubMoney_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (e.RowIndex >= 0 && (e.ColumnIndex == 4 || e.ColumnIndex == 5))
            {
                // 设置按钮颜色
                DataGridViewButtonCell? cell = (DataGridViewButtonCell)dataGridView_SubMoney.Rows[e.RowIndex].Cells[e.ColumnIndex];
                cell.FlatStyle = FlatStyle.Flat;
                cell.Style.ForeColor = Color.White;
                switch (e.ColumnIndex)
                {
                    case 4:
                        cell.Style.BackColor = Color.Green;
                        break;
                    case 5:
                        cell.Style.BackColor = Color.Red;
                        break;
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowSubMoneyOrderAsyncError", ex.ToString());
        }
    }

    /// <summary>
    /// PlatformLog
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void dataGridView_PlatformLog_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (e is { RowIndex: >= 0, ColumnIndex: 2 })
            {
                // 设置单元格颜色
                string cellValue = e.Value!.ToString()!;
                if (cellValue.Contains("飞单成功") || cellValue.Contains("飞单失败"))
                {
                    DataGridView? dgv = dataGridView_PlatformLog;

                    if (cellValue.Contains("飞单成功"))
                    {
                        dgv.Rows[e.RowIndex].Cells[1].Style.BackColor = Color.Green;
                        dgv.Rows[e.RowIndex].Cells[2].Style.BackColor = Color.Green;
                        dgv.Rows[e.RowIndex].Cells[3].Style.BackColor = Color.Green;
                    }
                    else if (cellValue.Contains("飞单失败"))
                    {
                        dgv.Rows[e.RowIndex].Cells[1].Style.BackColor = Color.Red;
                        dgv.Rows[e.RowIndex].Cells[2].Style.BackColor = Color.Red;
                        dgv.Rows[e.RowIndex].Cells[3].Style.BackColor = Color.Red;
                    }

                    dgv.Rows[e.RowIndex].Cells[1].Style.ForeColor = Color.White;
                    dgv.Rows[e.RowIndex].Cells[2].Style.ForeColor = Color.White;
                    dgv.Rows[e.RowIndex].Cells[3].Style.ForeColor = Color.White;
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowSubMoneyOrderAsyncError", ex.ToString());
        }
    }

    /// <summary>
    /// 汇总Report
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void dataGridView_HuiZongReport_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (dataGridView_HuiZongReport.Rows.Count <= 0)
            {
                return;
            }

            if (e is { RowIndex: > 0, ColumnIndex: 7 })
            {
                try
                {
                    string? cellValue = dataGridView_HuiZongReport.Rows[e.RowIndex].Cells[e.ColumnIndex].Value.ToString();
                    if (cellValue is "失败")
                    {
                        e.CellStyle!.BackColor = Color.Red;
                        e.CellStyle!.ForeColor = Color.White;
                        // e.CellStyle!.Font = new Font(dataGridView_MemberInfo.DefaultCellStyle.Font, FontStyle.Bold);
                    }
                }
                catch (Exception ex)
                {
                    await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowMemberInfoAsyncError", ex.ToString());
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowMemberInfoAsyncError", ex.ToString());
        }
    }
}