﻿using FreeSql.DataAnnotations;

namespace Robot.Models;

[Table(Name = "CloseTimeBetReport")]
public class CloseTimeBetReport
{
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    public DateTime Time { get; set; } = DateTime.Now;
    public string Issue { get; set; } = string.Empty;

    [Column(DbType = "nvarchar(4000)")] public string Report { get; set; } = string.Empty;
}