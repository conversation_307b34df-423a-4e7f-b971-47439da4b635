﻿namespace Robot.Models;

/// <summary>
/// 投注信息模型类 - 投注相关信息的数据传输对象
///
/// 功能说明：
/// - 用于投注信息的数据传输和临时存储
/// - 简化的投注数据结构，便于快速处理
/// - 主要用于消息解析和投注处理流程
/// - 不直接对应数据库表，作为DTO使用
///
/// 使用场景：
/// - 解析用户投注消息时的数据容器
/// - 投注处理流程中的数据传递
/// - 投注结果的临时计算和存储
/// - 与外部系统交互的数据格式
///
/// 与BetOrder的区别：
/// - BetInfo：轻量级的数据传输对象
/// - BetOrder：完整的数据库实体模型
/// - BetInfo用于临时处理，BetOrder用于持久化
///
/// 设计特点：
/// - 所有字段都是字符串类型，便于数据传输
/// - 结构简单，处理效率高
/// - 无数据库映射，纯内存对象
/// - 便于序列化和反序列化
///
/// 数据流转：
/// 用户消息 -> BetInfo -> 业务处理 -> BetOrder -> 数据库
/// </summary>
public class BetInfo
{
    /// <summary>
    /// 投注标识 - 投注的唯一标识符
    ///
    /// 用途：
    /// - 标识特定的投注信息
    /// - 用于投注处理的跟踪
    /// - 关联相关的业务数据
    ///
    /// 数据来源：
    /// - 系统生成的唯一标识
    /// - 消息ID或其他业务标识
    /// - 便于投注信息的管理
    ///
    /// 特点：
    /// - 字符串类型，灵活性高
    /// - 可以是数字、UUID或其他格式
    /// - 主要用于内部处理流程
    /// </summary>
    public string BetId { get; set; } = string.Empty;

    /// <summary>
    /// 投注内容 - 用户的具体投注选择
    ///
    /// 内容格式：
    /// - 番摊投注："1"、"2"、"3"、"4"
    /// - 大小投注："大"、"小"
    /// - 单双投注："单"、"双"
    /// - 复合投注：可能包含多个选择
    ///
    /// 用途：
    /// - 记录用户的投注选择
    /// - 用于投注验证和处理
    /// - 结算时的中奖判断依据
    ///
    /// 数据来源：
    /// - 从用户消息中解析获得
    /// - 经过格式化和验证处理
    /// - 确保内容的准确性
    ///
    /// 处理流程：
    /// - 消息解析 -> 内容提取 -> 格式验证 -> 存储到BetInfo
    /// </summary>
    public string BetContent { get; set; } = string.Empty;

    /// <summary>
    /// 投注赔率 - 该投注的赔率信息
    ///
    /// 数据格式：
    /// - 字符串形式的数字（如："3.8"）
    /// - 便于数据传输和显示
    /// - 需要转换为decimal进行计算
    ///
    /// 用途：
    /// - 记录投注时的赔率
    /// - 用于中奖金额的计算
    /// - 显示给用户的赔率信息
    ///
    /// 数据来源：
    /// - 从Odds表查询获得
    /// - 投注时锁定的赔率
    /// - 确保赔率的一致性
    ///
    /// 重要性：
    /// - 直接影响用户的收益
    /// - 系统盈利计算的基础
    /// - 投注吸引力的重要因素
    /// </summary>
    public string Odds { get; set; } = string.Empty;

    /// <summary>
    /// 中奖金额 - 该投注的潜在中奖金额
    ///
    /// 计算公式：
    /// - 中奖金额 = 投注金额 × 赔率
    /// - 字符串格式便于显示和传输
    ///
    /// 用途：
    /// - 显示用户的潜在收益
    /// - 投注确认时的信息展示
    /// - 中奖后的实际结算金额
    ///
    /// 数据特点：
    /// - 预计算的中奖金额
    /// - 字符串格式，便于处理
    /// - 需要转换为decimal进行实际结算
    ///
    /// 业务价值：
    /// - 提升用户的投注体验
    /// - 清晰展示投注收益
    /// - 增强投注的透明度
    ///
    /// 处理时机：
    /// - 投注解析时计算
    /// - 投注确认前显示
    /// - 中奖结算时使用
    /// </summary>
    public string WinAmount { get; set; } = string.Empty;
}