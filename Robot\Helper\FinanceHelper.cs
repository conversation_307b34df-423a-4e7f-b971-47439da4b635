﻿using Robot.ChatPlatform;
using Robot.Enum;

namespace Robot.Helper;

public static class FinanceHelper
{
    /// <summary>
    /// 处理一键返水
    /// </summary>
    public static async Task OneKeyRebateAsync()
    {
        try
        {
            CommonHelper.OneKeyRebateResult = string.Empty;
            await RobotHelper.RebateHandler();

            if (!string.IsNullOrEmpty(CommonHelper.OneKeyRebateResult))
            {
                CommonHelper.OneKeyRebateResult += "管理已操作全部返水,请各位注意查收.";
                await ChatHelper.SendGroupMessage(CommonHelper.OneKeyRebateResult);
                await DbHelper.AddLogAsync(EnumLogType.机器人, "操作提醒", "一键返水完毕.");
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "OneKeyRebateError", ex.ToString());
        }
    }
}