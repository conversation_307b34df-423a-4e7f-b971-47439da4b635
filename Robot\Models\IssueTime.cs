﻿using FreeSql.DataAnnotations;

namespace Robot.Models;

/// <summary>
/// 期号时间模型类 - 彩票期号与开奖时间的数据库存储模型
///
/// 功能说明：
/// - 存储彩票期号与对应开奖时间的映射关系
/// - 提供期号时间的持久化存储
/// - 支持开奖时间的查询和管理
/// - 用于开奖时间的预测和计算
///
/// 使用场景：
/// - 存储从平台获取的开奖时间信息
/// - 用户查询特定期号的开奖时间
/// - 系统预测下期开奖时间
/// - 开奖时间的历史数据管理
///
/// 数据库表：IssueTime
/// - 使用FreeSql ORM进行数据持久化
/// - 支持按期号查询开奖时间
/// - 提供开奖时间的结构化存储
///
/// 与KaiJiang的关系：
/// - KaiJiang：完整的开奖信息（期号+号码+时间）
/// - IssueTime：专门的期号时间映射表
/// - 两者可以互补，提供不同维度的数据查询
///
/// 业务价值：
/// - 提供准确的开奖时间信息
/// - 支持时间相关的业务逻辑
/// - 便于开奖时间的统计分析
/// - 增强系统的数据完整性
/// </summary>
[Table(Name = "IssueTime")]
public class IssueTime
{
    /// <summary>
    /// 记录唯一标识 - 数据库自增主键
    ///
    /// 特点：
    /// - 整型自增主键
    /// - 自动递增，无需手动设置
    /// - 作为记录的唯一标识符
    ///
    /// 用途：
    /// - 数据库记录的主键
    /// - 支持记录的唯一标识
    /// - 便于数据库操作和查询
    /// </summary>
    [Column(IsPrimary = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 彩票期号 - 彩票开奖期次的唯一标识
    ///
    /// 格式规范：
    /// - 台湾宾果：YYYYMMDD-XXX（如：20241211-001）
    /// - 一六八飞艇：YYYYMMDD-XXX（如：20241211-001）
    /// - 新一六八XL：YYYYMMDD-XXX（如：20241211-001）
    ///
    /// 用途：
    /// - 标识特定的开奖期次
    /// - 与开奖数据进行关联
    /// - 用户查询的关键字段
    /// - 时间序列的排序依据
    ///
    /// 特点：
    /// - 全局唯一，不会重复
    /// - 包含日期信息，便于理解
    /// - 格式统一，便于处理
    ///
    /// 业务意义：
    /// - 开奖数据的核心标识
    /// - 用户投注的目标期号
    /// - 历史查询的重要字段
    /// - 与KaiJiang表的关联键
    /// </summary>
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 开奖时间 - 该期号对应的开奖时间戳
    ///
    /// 数据类型：
    /// - DateTime类型，精确到秒
    /// - 支持完整的日期时间操作
    /// - 便于时间计算和比较
    ///
    /// 用途：
    /// - 记录具体的开奖时间
    /// - 用户查询开奖历史
    /// - 系统时间管理和计算
    /// - 开奖时间的统计分析
    ///
    /// 数据来源：
    /// - 从彩票平台API获取
    /// - 确保时间的官方性和准确性
    /// - 与开奖号码数据同步获取
    ///
    /// 业务价值：
    /// - 提供准确的开奖时间信息
    /// - 支持时间相关的业务逻辑
    /// - 便于开奖时间的预测
    /// - 增强用户体验和信任度
    ///
    /// 与KaiJiang.Time的区别：
    /// - IssueTime.OpenTime：DateTime类型，便于计算
    /// - KaiJiang.Time：string类型，便于显示
    /// - 两者互补，满足不同的业务需求
    /// </summary>
    public DateTime OpenTime { get; set; }
}