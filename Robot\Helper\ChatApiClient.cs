using System.Diagnostics;
using System.Drawing.Imaging;
using Flurl;
using Flurl.Http;

namespace Robot.Helper;

public class ChatApiClient
{
    private readonly string _baseUrl;

    public ChatApiClient(string baseUrl = "http://localhost:3000")
    {
        _baseUrl = baseUrl;
        FlurlHttp.Configure(settings =>
        {
            settings.Timeout = TimeSpan.FromSeconds(3);
        });
    }

    #region 用户管理

    /// <summary>
    /// 获取用户列表
    /// </summary>
    public async Task<List<User>> GetUsersAsync()
    {
        try
        {
            return await _baseUrl.AppendPathSegment("api/users").GetJsonAsync<List<User>>();
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"获取用户列表失败: {error}", ex);
        }
    }

    /// <summary>
    /// 添加用户
    /// </summary>
    public async Task<UserResponse> AddUserAsync(string nickname)
    {
        try
        {
            return await _baseUrl
                .AppendPathSegment("api/users")
                .PostJsonAsync(new { nickname })
                .ReceiveJson<UserResponse>();
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"添加用户失败: {error}", ex);
        }
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    public async Task DeleteUserAsync(string uid)
    {
        try
        {
            await _baseUrl.AppendPathSegment($"api/users/{uid}").DeleteAsync();
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"删除用户失败: {error}", ex);
        }
    }

    #endregion

    #region 消息发送

    /// <summary>
    /// 发送文本消息
    /// </summary>
    public async Task SendTextMessageAsync(string uid, string content)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(content))
            {
                return;
            }

            await _baseUrl
                .AppendPathSegment("api/send")
                .PostJsonAsync(
                    new
                    {
                        uid,
                        content,
                        type = "text",
                    }
                );
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            Debug.WriteLine(error);
        }
    }

    /// <summary>
    /// 发送图片消息
    /// </summary>
    public async Task SendImageMessageAsync(string uid, string imagePath)
    {
        try
        {
            var imageUrl = await UploadImageAsync(imagePath);
            await SendMessageInternalAsync(uid, imageUrl, "image");
        }
        catch (Exception ex)
        {
            Debug.WriteLine(ex.ToString());
        }
    }

    /// <summary>
    /// 发送Base64图片
    /// </summary>
    public async Task SendBase64ImageAsync(string uid, string base64Image)
    {
        try
        {
            var tempFile = Path.GetTempFileName() + ".png";
            try
            {
                var base64Data = base64Image.Contains(",")
                    ? base64Image.Split(',')[1]
                    : base64Image;

                await File.WriteAllBytesAsync(tempFile, Convert.FromBase64String(base64Data));
                await SendImageMessageAsync(uid, tempFile);
            }
            finally
            {
                if (File.Exists(tempFile))
                {
                    File.Delete(tempFile);
                }
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"发送Base64图片失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 发送剪贴板图片
    /// </summary>
    public async Task SendClipboardImageAsync(string uid)
    {
        if (!Clipboard.ContainsImage())
        {
            throw new InvalidOperationException("剪贴板中没有图片");
        }

        try
        {
            using var image = Clipboard.GetImage();
            var tempFile = Path.GetTempFileName() + ".png";
            try
            {
                image.Save(tempFile, ImageFormat.Png);
                await SendImageMessageAsync(uid, tempFile);
            }
            finally
            {
                if (File.Exists(tempFile))
                {
                    File.Delete(tempFile);
                }
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"发送剪贴板图片失败: {ex.Message}", ex);
        }
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 上传图片
    /// </summary>
    private async Task<string> UploadImageAsync(string imagePath)
    {
        if (!File.Exists(imagePath))
        {
            throw new FileNotFoundException("图片文件不存在", imagePath);
        }

        var extension = Path.GetExtension(imagePath).ToLower();
        if (!new[] { ".jpg", ".jpeg", ".png", ".gif" }.Contains(extension))
        {
            throw new ArgumentException("不支持的图片格式");
        }

        try
        {
            using var stream = File.OpenRead(imagePath);
            var fileName = Path.GetFileName(imagePath);

            var response = await _baseUrl
                .AppendPathSegment("uploads")
                .PostMultipartAsync(mp => mp.AddFile("file", stream, fileName))
                .ReceiveJson<UploadResponse>();

            return response.Url;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"上传图片失败: {error}", ex);
        }
    }

    /// <summary>
    /// 内部发送消息方法
    /// </summary>
    private async Task SendMessageInternalAsync(string uid, string content, string type)
    {
        try
        {
            await _baseUrl
                .AppendPathSegment("api/send")
                .PostJsonAsync(
                    new
                    {
                        uid,
                        content,
                        type,
                    }
                );
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"发送消息失败: {error}", ex);
        }
    }

    #endregion
}

#region 数据模型

public class User
{
    public int Id { get; set; }
    public string Uid { get; set; }
    public string Guid { get; set; }
    public string Nickname { get; set; }
    public string Status { get; set; }
    public DateTime LastSeen { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class UserResponse
{
    public string Uid { get; set; }
    public string Guid { get; set; }
    public string Nickname { get; set; }
}

public class UploadResponse
{
    public string Url { get; set; }
}

#endregion
