﻿using Flurl.Http;
using Newtonsoft.Json.Linq;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;

namespace Robot.ChatPlatform;

/// <summary>
/// OneChat平台助手类
/// 提供与OneChat聊天平台交互的方法，包括获取机器人信息、获取群组、获取用户昵称、
/// 发送群消息和图片等功能
/// </summary>
public static class OneChatHelper
{
    /// <summary>
    /// OneChat服务器地址
    /// </summary>
    private static string Host { get; set; } = @"http://127.0.0.1:3001";

    /// <summary>
    /// 机器人用户ID
    /// </summary>
    private static string Uid { get; set; } = @"admin";

    /// <summary>
    /// 获取机器人信息
    /// 设置机器人的账号和昵称
    /// </summary>
    public static async Task GetRobotInfo()
    {
        RobotHelper.RobotInfo.Account = "admin";
        RobotHelper.RobotInfo.NickName = "系统管理员";
        await Task.Delay(0);
    }

    /// <summary>
    /// 获取群组信息
    /// 将OneChat群组添加到群组字典中
    /// </summary>
    public static async Task GetGroup()
    {
        RobotHelper.GroupDic.TryAdd("OneChat", "一起聊吧");
        await Task.Delay(0);
    }

    /// <summary>
    /// 获取用户昵称
    /// </summary>
    /// <param name="uid">用户ID</param>
    /// <returns>用户昵称，如果获取失败则返回空字符串</returns>
    public static async Task<string> GetNick(string uid)
    {
        try
        {
            string url = $"{Host}/api/users/getNickName?uid={uid}";
            string response = await url
                .WithTimeout(TimeSpan.FromSeconds(3))
                .GetStringAsync();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("nickname"))
            {
                JObject jObject = JObject.Parse(response);
                string nickName = jObject["nickname"]!.ToString();
                return nickName;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, ex.ToString());
        }

        return "";
    }

    /// <summary>
    /// 发送群消息
    /// </summary>
    /// <param name="content">消息内容</param>
    public static async Task SendGroupMessage(string content)
    {
        try
        {
            content = content.Replace("\r", Environment.NewLine);

            // 发送请求
            await $"{Host}/api/messages/sendText"
                .PostJsonAsync(new
                {
                    Uid,
                    Content = content
                });
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, ex.ToString());
        }
    }

    /// <summary>
    /// 发送@特定用户的群消息
    /// </summary>
    /// <param name="content">消息内容</param>
    /// <param name="atAccount">要@的用户账号</param>
    public static async Task SendGroupMessage(string content, string atAccount)
    {
        try
        {
            content = content.Replace("\r", Environment.NewLine);
            string nickName = (await DbHelper.FSql.Select<Member>().Where(a => a.Account.Equals(atAccount)).ToOneAsync()).昵称;

            // 发送请求
            await $"{Host}/api/messages/sendText"
                .PostJsonAsync(new
                {
                    Uid,
                    content = "@" + nickName + content
                });
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, ex.ToString());
        }
    }

    /// <summary>
    /// 发送图片消息
    /// </summary>
    /// <param name="workGroupId">工作群组ID（在OneChat中未实际使用）</param>
    /// <param name="imagePath">图片文件路径</param>
    public static async Task SendImage(string workGroupId, string imagePath)
    {
        try
        {
            // 验证文件存在
            if (!File.Exists(imagePath))
            {
                Console.WriteLine($@"文件不存在: {imagePath}");
                return;
            }

            // 验证文件类型
            string extension = Path.GetExtension(imagePath).ToLower();
            if (extension != ".jpg" && extension != ".jpeg" && extension != ".png" && extension != ".gif")
            {
                Console.WriteLine(@"不支持的图片格式，仅支持JPG、PNG和GIF");
                return;
            }

            // 构建API URL
            string apiUrl = $"{Host}/api/messages/sendImage";

            // 上传图片
            await apiUrl
                .PostMultipartAsync(mp =>
                {
                    mp.AddString("Uid", Uid);
                    mp.AddFile("image", imagePath);
                });
        }
        catch (FlurlHttpException ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, ex.ToString());
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, ex.ToString());
        }
    }
}