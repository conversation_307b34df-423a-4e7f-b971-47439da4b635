﻿using Flurl.Http;
using Newtonsoft.Json;
using Robot.Config;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;
using Sunny.UI;
using System.ComponentModel;

namespace Robot.Ui
{
    /// <summary>
    /// 赔率管理窗体 - Robot彩票系统的核心配置模块
    ///
    /// 主要功能：
    /// 1. 赔率配置管理：支持多种彩票游戏的赔率设置和修改
    /// 2. 投注限额控制：管理最低投注、最高投注和总投注限额
    /// 3. 数据验证机制：确保赔率配置的合理性和有效性
    /// 4. 配置文件同步：实现内存数据与配置文件的双向同步
    /// 5. 远程配置更新：支持从服务器获取最新的默认赔率配置
    ///
    /// 支持的彩票游戏类型：
    /// - 台湾宾果系列：台湾宾果1、台湾宾果2、台湾宾果3
    /// - 168飞艇系列：前3、中3、后3
    /// - 新168XL系列：前、中、后
    ///
    /// 投注玩法类型：
    /// - 正投：1正、2正、3正、4正（直接投注某个号码）
    /// - 番投：1番、2番、3番、4番（番摊玩法，赔率较高）
    /// - 角投：12角、23角、34角、14角（两个号码的组合投注）
    /// - 念投：1念2、1念3等（特殊的组合投注方式）
    /// - 单双投注：单、双（投注开奖结果的单双性）
    ///
    /// 技术特点：
    /// - 使用DataGridView实现直观的表格编辑界面
    /// - 采用数据绑定技术实现UI与数据的自动同步
    /// - 完善的数据验证和异常处理机制
    /// - 支持异步文件操作和远程数据获取
    /// - 详细的操作日志记录和用户反馈
    ///
    /// 作者：Robot开发团队
    /// 创建时间：2024年
    /// 最后修改：2024年
    /// </summary>
    public partial class FormOdds : UIForm
    {
        #region 私有字段

        /// <summary>
        /// 当前选中的彩票游戏名称
        /// 用于标识用户当前正在编辑的彩票游戏类型
        /// 在用户切换游戏选择时更新，在保存数据时使用
        /// </summary>
        private string _currentLotteryName = string.Empty;

        /// <summary>
        /// 赔率数据绑定源
        /// 作为DataGridView的数据源，支持双向数据绑定
        /// 使用BindingList确保数据变更时UI能够自动更新
        /// 存储当前选中游戏的所有投注项目的赔率配置信息
        /// </summary>
        private readonly BindingList<BetOptionDisplay> _oddsDataSource = new();

        #endregion

        #region 构造函数和初始化

        /// <summary>
        /// FormOdds构造函数
        /// 初始化窗体组件并配置DataGridView控件
        /// </summary>
        public FormOdds()
        {
            // 初始化窗体设计器生成的组件
            InitializeComponent();

            // 初始化DataGridView的列结构和数据绑定
            InitializeDataGridView();
        }

        /// <summary>
        /// 初始化DataGridView控件的基本设置和数据绑定
        ///
        /// 功能说明：
        /// 1. 配置DataGridView的外观和行为属性
        /// 2. 创建用于显示赔率信息的列结构
        /// 3. 建立数据源与控件的绑定关系
        ///
        /// 设置的属性：
        /// - AutoGenerateColumns: 禁用自动生成列，使用手动定义的列
        /// - AllowUserToAddRows/DeleteRows: 禁止用户添加或删除行
        /// - SelectionMode: 设置为整行选择模式
        /// - MultiSelect: 禁用多选功能
        /// - RowHeadersVisible: 隐藏行标题
        /// - BackgroundColor: 设置背景色为白色
        /// - BorderStyle: 设置3D边框样式
        /// </summary>
        private void InitializeDataGridView()
        {
            // 设置DataGridView基本属性 - 控制数据显示和用户交互行为
            dataGridView1.AutoGenerateColumns = false; // 禁用自动生成列，使用手动定义
            dataGridView1.AllowUserToAddRows = false; // 禁止用户添加新行
            dataGridView1.AllowUserToDeleteRows = false; // 禁止用户删除行
            dataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect; // 设置为整行选择模式
            dataGridView1.MultiSelect = false; // 禁用多选功能
            dataGridView1.RowHeadersVisible = false; // 隐藏行标题列
            dataGridView1.BackgroundColor = Color.White; // 设置背景色为白色
            dataGridView1.BorderStyle = BorderStyle.Fixed3D; // 设置3D边框样式

            // 创建并配置DataGridView的列结构
            // 包括投注项目、赔率、最低投注、最高投注、总投注限额等列
            CreateDataGridViewColumns();

            // 建立数据绑定关系
            // 将BindingList数据源绑定到DataGridView，实现双向数据同步
            dataGridView1.DataSource = _oddsDataSource;
        }

        /// <summary>
        /// 创建DataGridView的列结构定义
        ///
        /// 功能说明：
        /// 定义用于显示赔率配置信息的5个列：
        /// 1. 投注项目列：显示投注类型（如1正、2番、大、小等）
        /// 2. 赔率列：显示中奖时的赔付倍数
        /// 3. 最低投注列：显示单次投注的最小金额限制
        /// 4. 最高投注列：显示单次投注的最大金额限制
        /// 5. 总投注限额列：显示该项目的总投注上限
        ///
        /// 列配置特点：
        /// - 投注项目列设为只读，防止用户修改项目名称
        /// - 数值列使用居中对齐，提升视觉效果
        /// - 赔率列显示3位小数，投注金额列显示整数
        /// - 合理的列宽设置，确保内容完整显示
        /// </summary>
        private void CreateDataGridViewColumns()
        {
            // 第1列：投注项目列
            // 显示投注类型名称，如"1正"、"2番"、"大"、"小"等
            // 设为只读，防止用户意外修改项目标识
            DataGridViewTextBoxColumn contentColumn = new DataGridViewTextBoxColumn
            {
                Name = "Content", // 列的内部标识名称
                HeaderText = @"投注项目", // 列标题显示文本
                DataPropertyName = "Content", // 绑定到数据源的属性名
                ReadOnly = true, // 设为只读，不允许编辑
                Width = 120, // 列宽度（像素）
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter } // 居中对齐
            };

            // 第2列：赔率列
            // 显示中奖时的赔付倍数，支持用户编辑
            // 使用3位小数格式显示，如1.930、3.835等
            DataGridViewTextBoxColumn oddsColumn = new DataGridViewTextBoxColumn
            {
                Name = "Odds", // 列的内部标识名称
                HeaderText = @"赔率", // 列标题显示文本
                DataPropertyName = "Odds", // 绑定到数据源的属性名
                Width = 100, // 列宽度（像素）
                DefaultCellStyle =
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter, // 居中对齐
                    Format = "F3" // 格式化为3位小数
                }
            };

            // 第3列：最低投注列
            // 显示单次投注的最小金额限制，支持用户编辑
            // 使用整数格式显示，如1、5、10等
            DataGridViewTextBoxColumn minStakeColumn = new DataGridViewTextBoxColumn
            {
                Name = "MinStake", // 列的内部标识名称
                HeaderText = @"最低投注", // 列标题显示文本
                DataPropertyName = "MinStake", // 绑定到数据源的属性名
                Width = 100, // 列宽度（像素）
                DefaultCellStyle =
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter, // 居中对齐
                    Format = "F0" // 格式化为整数（无小数位）
                }
            };

            // 第4列：最高投注列
            // 显示单次投注的最大金额限制，支持用户编辑
            // 使用整数格式显示，如1000、5000、30000等
            DataGridViewTextBoxColumn maxStakeColumn = new DataGridViewTextBoxColumn
            {
                Name = "MaxStake", // 列的内部标识名称
                HeaderText = @"最高投注", // 列标题显示文本
                DataPropertyName = "MaxStake", // 绑定到数据源的属性名
                Width = 100, // 列宽度（像素）
                DefaultCellStyle =
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter, // 居中对齐
                    Format = "F0" // 格式化为整数（无小数位）
                }
            };

            // 第5列：总投注限额列
            // 显示该投注项目的总投注上限，支持用户编辑
            // 使用整数格式显示，通常与最高投注相同或更大
            DataGridViewTextBoxColumn totalStakeColumn = new DataGridViewTextBoxColumn
            {
                Name = "TotalStake", // 列的内部标识名称
                HeaderText = @"总投注限额", // 列标题显示文本
                DataPropertyName = "TotalStake", // 绑定到数据源的属性名
                Width = 120, // 列宽度（像素）
                DefaultCellStyle =
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter, // 居中对齐
                    Format = "F0" // 格式化为整数（无小数位）
                }
            };

            // 将所有列添加到DataGridView控件
            // 按照从左到右的顺序：投注项目 → 赔率 → 最低投注 → 最高投注 → 总投注限额
            dataGridView1.Columns.AddRange(new DataGridViewColumn[]
            {
                contentColumn, // 投注项目列
                oddsColumn, // 赔率列
                minStakeColumn, // 最低投注列
                maxStakeColumn, // 最高投注列
                totalStakeColumn // 总投注限额列
            });
        }

        #endregion

        #region 窗体事件处理

        /// <summary>
        /// 窗体加载事件处理程序
        ///
        /// 功能说明：
        /// 1. 根据用户配置加载可用的彩票游戏类型到下拉框
        /// 2. 自动选择第一个可用的游戏类型作为默认选项
        /// 3. 触发游戏选择变更事件，加载对应的赔率数据
        ///
        /// 执行流程：
        /// 窗体显示 → 加载游戏列表 → 选择默认游戏 → 加载赔率数据 → 显示完成
        /// </summary>
        /// <param name="sender">事件发送者（FormOdds实例）</param>
        /// <param name="e">事件参数</param>
        private void FormOdds_Load(object sender, EventArgs e)
        {
            // 根据用户设置加载可用的彩票游戏类型
            // 只有在用户配置中启用的游戏才会显示在下拉框中
            LoadAvailableLotteryGames();

            // 如果有可用游戏，默认选择第一个
            // 这将触发SelectedIndexChanged事件，自动加载对应的赔率数据
            if (comboBox_Lottery.Items.Count > 0)
            {
                comboBox_Lottery.SelectedIndex = 0;
            }
        }

        /// <summary>
        /// 彩票游戏选择改变事件处理程序
        ///
        /// 功能说明：
        /// 当用户在下拉框中选择不同的彩票游戏时触发
        /// 自动加载选中游戏的赔率配置数据到DataGridView中显示
        ///
        /// 处理逻辑：
        /// 1. 验证选择项的有效性
        /// 2. 更新当前游戏名称变量
        /// 3. 调用数据加载方法显示赔率信息
        ///
        /// 触发时机：
        /// - 用户手动选择下拉框项目时
        /// - 程序代码设置SelectedIndex属性时
        /// </summary>
        /// <param name="sender">事件发送者（ComboBox控件）</param>
        /// <param name="e">事件参数</param>
        private void comboBox_Lottery_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 验证选择项的有效性，防止空引用异常
            if (comboBox_Lottery.SelectedItem == null) return;

            // 更新当前选中的游戏名称
            // 这个变量将在保存数据时使用，用于标识要更新的游戏配置
            _currentLotteryName = comboBox_Lottery.SelectedItem.ToString()!;

            // 加载选中游戏的赔率数据到DataGridView
            LoadOddsDataToGrid(_currentLotteryName);
        }

        #endregion

        #region 数据加载和显示

        /// <summary>
        /// 加载可用的彩票游戏类型到下拉框
        ///
        /// 功能说明：
        /// 根据用户配置文件中的启用设置，动态加载可用的彩票游戏类型
        /// 只有配置为启用状态（值为1）的游戏才会显示在下拉框中
        ///
        /// 支持的游戏类型：
        /// - 台湾宾果系列：台湾宾果1、台湾宾果2、台湾宾果3
        /// - 168飞艇系列：一六八飞艇前3、一六八飞艇中3、一六八飞艇后3
        /// - 新168XL系列：新一六八XL前、新一六八XL中、新一六八XL后
        ///
        /// 配置检查逻辑：
        /// 通过UserSetting.Current对象读取用户配置，检查每个游戏的启用状态
        /// 启用值为1表示该游戏可用，为0表示禁用
        /// </summary>
        private void LoadAvailableLotteryGames()
        {
            // 清空下拉框现有项目，准备重新加载
            comboBox_Lottery.Items.Clear();

            // 台湾宾果系列游戏加载
            // 检查台湾宾果1是否启用
            if (UserSetting.Current.启用台湾宾果1 == 1)
            {
                comboBox_Lottery.Items.Add("台湾宾果1");
            }

            // 检查台湾宾果2是否启用
            if (UserSetting.Current.启用台湾宾果2 == 1)
            {
                comboBox_Lottery.Items.Add("台湾宾果2");
            }

            // 检查台湾宾果3是否启用
            if (UserSetting.Current.启用台湾宾果3 == 1)
            {
                comboBox_Lottery.Items.Add("台湾宾果3");
            }

            // 168飞艇系列游戏加载
            // 检查168飞艇前3是否启用
            if (UserSetting.Current.启用168飞艇前3 == 1)
            {
                comboBox_Lottery.Items.Add("一六八飞艇前3");
            }

            // 检查168飞艇中3是否启用
            if (UserSetting.Current.启用168飞艇中3 == 1)
            {
                comboBox_Lottery.Items.Add("一六八飞艇中3");
            }

            // 检查168飞艇后3是否启用
            if (UserSetting.Current.启用168飞艇后3 == 1)
            {
                comboBox_Lottery.Items.Add("一六八飞艇后3");
            }
        }

        /// <summary>
        /// 加载指定彩票游戏的赔率数据到DataGridView
        ///
        /// 功能说明：
        /// 1. 从内存中的赔率配置数据加载指定游戏的赔率信息
        /// 2. 将数据转换为适合界面显示的格式
        /// 3. 按照预定义的顺序排列投注项目
        /// 4. 更新DataGridView的显示内容
        ///
        /// 数据来源：
        /// CommonHelper.LotteryGames - 全局赔率配置字典
        ///
        /// 处理流程：
        /// 清空现有数据 → 验证游戏存在 → 获取赔率配置 → 按序加载数据 → 刷新显示
        ///
        /// 异常处理：
        /// 捕获所有异常并显示用户友好的错误信息
        /// </summary>
        /// <param name="lotteryName">彩票游戏名称（如"台湾宾果1"、"一六八飞艇前3"等）</param>
        private void LoadOddsDataToGrid(string lotteryName)
        {
            try
            {
                // 清空DataGridView的现有数据
                // 使用Clear()方法移除BindingList中的所有项目
                // 由于数据绑定的存在，这将自动清空DataGridView的显示
                _oddsDataSource.Clear();

                // 检查指定的彩票游戏是否存在于配置中
                // 防止因游戏名称错误或配置缺失导致的异常
                if (!CommonHelper.LotteryGames.TryGetValue(lotteryName, out var gameOptions))
                {
                    this.ShowWarningDialog($"未找到【{lotteryName}】的赔率配置！");
                    return;
                }

                // 获取该游戏的完整赔率配置字典
                // 字典结构：Key=投注项目名称（如"1正"、"2番"），Value=赔率配置对象

                // 按照预定义的顺序加载数据到界面
                // 确保投注项目按照逻辑分组显示（正投→番投→角投→念投→单双）
                LoadOddsDataInOrder(gameOptions);

                // 强制刷新DataGridView的显示
                // 虽然数据绑定会自动更新，但手动刷新确保界面立即响应
                dataGridView1.Refresh();
            }
            catch (Exception ex)
            {
                // 捕获所有可能的异常，显示用户友好的错误信息
                // 避免程序崩溃，提升用户体验
                this.ShowErrorDialog($"加载赔率数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 按照预定义的逻辑顺序加载赔率数据到界面
        ///
        /// 功能说明：
        /// 1. 按照投注玩法的逻辑分组顺序显示数据
        /// 2. 确保界面显示的一致性和用户体验
        /// 3. 处理配置中可能存在的额外投注项目
        ///
        /// 显示顺序逻辑：
        /// - 正投类型：1正、2正、3正、4正（直接投注单个号码）
        /// - 番投类型：1番、2番、3番、4番（番摊玩法，赔率较高）
        /// - 角投类型：12角、23角、34角、14角（两个号码的组合投注）
        /// - 念投类型：1念2、1念3...4念3（特殊组合投注方式）
        /// - 单双类型：单、双（投注开奖结果的单双性）
        ///
        /// 处理策略：
        /// 1. 优先按预定义顺序添加存在的项目
        /// 2. 补充添加不在预定义顺序中的其他项目
        /// 3. 确保所有配置项目都能正确显示
        ///
        /// 数据转换：
        /// 将BetOption对象转换为BetOptionDisplay对象，适配界面数据绑定需求
        /// </summary>
        /// <param name="gameOptions">游戏的赔率配置字典</param>
        private void LoadOddsDataInOrder(Dictionary<string, BetOption> gameOptions)
        {
            // 定义投注项目的显示顺序数组
            // 按照玩法类型分组，确保界面显示的逻辑性和一致性
            string[] displayOrder =
            [
                // 正投类型 - 直接投注单个号码，赔率相对较低
                "1正", "2正", "3正", "4正",

                // 番投类型 - 番摊玩法，赔率较高
                "1番", "2番", "3番", "4番",

                // 角投类型 - 两个号码的组合投注
                "12角", "23角", "34角", "14角",

                // 念投类型 - 特殊的组合投注方式，赔率中等
                "1念2", "1念3", "1念4", "2念1", "2念3", "2念4",
                "3念1", "3念2", "3念4", "4念1", "4念2", "4念3",

                // 单双类型 - 投注开奖结果的单双性
                "单", "双"
            ];

            // 第一轮：按照预定义顺序添加存在的投注项目
            // 遍历显示顺序数组，查找并添加配置中存在的项目
            foreach (string content in displayOrder)
            {
                // 检查当前项目是否在游戏配置中存在
                if (gameOptions.TryGetValue(content, out var option))
                {
                    // 将BetOption转换为BetOptionDisplay对象
                    // BetOptionDisplay实现了INotifyPropertyChanged，支持数据绑定
                    BetOptionDisplay display = BetOptionDisplay.FromBetOption(content, option);

                    // 添加到数据源，由于数据绑定，界面会自动更新
                    _oddsDataSource.Add(display);
                }
            }

            // 第二轮：添加不在预定义顺序中的其他投注项目
            // 处理配置文件中可能存在的额外或自定义投注项目
            foreach (KeyValuePair<string, BetOption> kvp in gameOptions)
            {
                // 检查该项目是否已经在第一轮中添加过
                if (!displayOrder.Contains(kvp.Key))
                {
                    // 创建显示对象并添加到数据源
                    BetOptionDisplay display = BetOptionDisplay.FromBetOption(kvp.Key, kvp.Value);
                    _oddsDataSource.Add(display);
                }
            }
        }

        #endregion

        #region 按钮事件处理

        /// <summary>
        /// 保存修改按钮点击事件处理程序
        ///
        /// 功能说明：
        /// 1. 验证用户在DataGridView中修改的赔率数据的有效性
        /// 2. 将修改后的数据同步到内存中的全局配置
        /// 3. 将更新后的配置保存到本地JSON配置文件
        /// 4. 提供用户反馈和操作日志记录
        ///
        /// 执行流程：
        /// 检查选择状态 → 验证数据有效性 → 更新内存数据 → 保存配置文件 → 用户反馈 → 记录日志
        ///
        /// 数据验证规则：
        /// - 赔率必须在(0, 100]范围内
        /// - 最低投注必须大于0
        /// - 最高投注不能小于最低投注
        /// - 总投注限额不能小于最高投注
        ///
        /// 异常处理：
        /// 捕获所有异常，显示用户友好的错误信息，并记录详细的错误日志
        ///
        /// 异步操作：
        /// 文件保存和日志记录使用异步操作，避免阻塞UI线程
        /// </summary>
        /// <param name="sender">事件发送者（保存按钮）</param>
        /// <param name="e">事件参数</param>
        private async void uiButton_SaveChange_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否已选择要修改的彩票游戏
                // 防止在未选择游戏的情况下执行保存操作
                if (string.IsNullOrEmpty(_currentLotteryName))
                {
                    this.ShowWarningDialog("请先选择要修改的彩票游戏！");
                    return;
                }

                // 验证用户修改的赔率数据的有效性
                // 包括数值范围、逻辑关系等多项检查
                if (!ValidateOddsData())
                {
                    // 验证失败时，ValidateOddsData方法已显示具体错误信息
                    return;
                }

                // 将界面上的修改同步到内存中的全局赔率配置
                // 更新CommonHelper.LotteryGames字典中对应游戏的配置
                UpdateLotteryGamesData();

                // 将更新后的完整配置保存到本地JSON文件
                // 使用异步操作避免阻塞UI线程
                await SaveOddsToConfigFile();

                // 显示成功提示信息给用户
                this.ShowSuccessDialog("赔率修改保存成功！");

                // 记录操作日志到数据库
                // 便于后续的操作追踪和问题排查
                await DbHelper.AddLogAsync(EnumLogType.机器人, "修改赔率", $"成功修改【{_currentLotteryName}】的赔率配置");
            }
            catch (Exception ex)
            {
                // 捕获所有可能的异常，提供用户友好的错误反馈
                this.ShowErrorDialog($"保存赔率失败：{ex.Message}");

                // 记录详细的错误日志，包含异常堆栈信息
                await DbHelper.AddLogAsync(EnumLogType.机器人, "保存赔率失败", ex.ToString());
            }
        }

        #endregion

        #region 数据验证和处理

        /// <summary>
        /// 验证赔率数据的有效性和逻辑正确性
        ///
        /// 功能说明：
        /// 对用户在DataGridView中修改的所有赔率数据进行全面验证
        /// 确保数据符合业务规则和系统要求，防止无效配置导致系统异常
        ///
        /// 验证规则详解：
        ///
        /// 1. 赔率验证：
        ///    - 必须大于0：确保有正向的赔付能力
        ///    - 不能超过100：防止过高赔率导致系统风险
        ///    - 合理范围：通常在1.5-10之间为正常范围
        ///
        /// 2. 最低投注验证：
        ///    - 必须大于0：确保有实际的投注门槛
        ///    - 通常设置为1元或更高
        ///
        /// 3. 最高投注验证：
        ///    - 必须大于0：确保有投注上限控制
        ///    - 不能小于最低投注：保证逻辑一致性
        ///    - 用于控制单次投注风险
        ///
        /// 4. 总投注限额验证：
        ///    - 必须大于0：确保有总量控制
        ///    - 不能小于最高投注：保证逻辑一致性
        ///    - 用于控制该项目的总体风险敞口
        ///
        /// 错误处理：
        /// 一旦发现验证失败，立即显示具体的错误信息并返回false
        /// 错误信息包含具体的投注项目名称和违反的规则
        ///
        /// 返回值：
        /// true - 所有数据验证通过，可以继续保存操作
        /// false - 存在验证失败的数据，需要用户修正
        /// </summary>
        /// <returns>验证是否通过：true=通过，false=失败</returns>
        private bool ValidateOddsData()
        {
            // 遍历数据源中的每一个投注项目进行验证
            foreach (BetOptionDisplay item in _oddsDataSource)
            {
                // 验证赔率的有效性
                // 赔率必须为正数，表示中奖时的赔付倍数
                if (item.Odds <= 0)
                {
                    this.ShowWarningDialog($"【{item.Content}】的赔率必须大于0！");
                    return false;
                }

                // 验证赔率的合理性上限
                // 防止设置过高的赔率导致系统风险
                if (item.Odds > 100)
                {
                    this.ShowWarningDialog($"【{item.Content}】的赔率不能超过100！");
                    return false;
                }

                // 验证最低投注金额
                // 确保有合理的投注门槛，防止过小金额的投注
                if (item.MinStake <= 0)
                {
                    this.ShowWarningDialog($"【{item.Content}】的最低投注必须大于0！");
                    return false;
                }

                // 验证最高投注金额的基本有效性
                // 最高投注用于控制单次投注的风险上限
                if (item.MaxStake <= 0)
                {
                    this.ShowWarningDialog($"【{item.Content}】的最高投注必须大于0！");
                    return false;
                }

                // 验证投注金额的逻辑关系
                // 最高投注不能小于最低投注，确保配置的逻辑一致性
                if (item.MaxStake < item.MinStake)
                {
                    this.ShowWarningDialog($"【{item.Content}】的最高投注不能小于最低投注！");
                    return false;
                }

                // 验证总投注限额的基本有效性
                // 总投注限额用于控制该项目的总体风险敞口
                if (item.TotalStake <= 0)
                {
                    this.ShowWarningDialog($"【{item.Content}】的总投注限额必须大于0！");
                    return false;
                }

                // 验证总投注限额与最高投注的逻辑关系
                // 总投注限额不能小于最高投注，确保配置的合理性
                if (item.TotalStake < item.MaxStake)
                {
                    this.ShowWarningDialog($"【{item.Content}】的总投注限额不能小于最高投注！");
                    return false;
                }
            }

            // 所有验证项目都通过，返回成功
            return true;
        }

        /// <summary>
        /// 更新内存中的全局赔率配置数据
        ///
        /// 功能说明：
        /// 将用户在界面上修改的赔率数据同步到内存中的全局配置对象
        /// 确保后续的投注验证和结算计算使用最新的赔率配置
        ///
        /// 数据流向：
        /// DataGridView界面数据 → BetOptionDisplay对象 → BetOption对象 → CommonHelper.LotteryGames全局配置
        ///
        /// 处理逻辑：
        /// 1. 检查并确保目标游戏的配置字典存在
        /// 2. 遍历界面数据源中的所有投注项目
        /// 3. 将每个BetOptionDisplay对象转换为BetOption对象
        /// 4. 更新全局配置字典中对应的赔率配置
        ///
        /// 数据结构：
        /// CommonHelper.LotteryGames[游戏名称][投注项目] = BetOption配置对象
        ///
        /// 线程安全：
        /// 该方法在UI线程中执行，不涉及多线程并发问题
        /// </summary>
        private void UpdateLotteryGamesData()
        {
            // 确保目标游戏的配置字典存在
            // 如果是新游戏或配置丢失，创建新的空字典
            if (!CommonHelper.LotteryGames.ContainsKey(_currentLotteryName))
            {
                CommonHelper.LotteryGames[_currentLotteryName] = new Dictionary<string, BetOption>();
            }

            // 遍历界面数据源中的所有投注项目
            // 将用户的修改同步到全局配置中
            foreach (BetOptionDisplay item in _oddsDataSource)
            {
                // 将界面显示对象转换为配置对象
                // BetOptionDisplay.ToBetOption()方法负责数据转换
                CommonHelper.LotteryGames[_currentLotteryName][item.Content] = item.ToBetOption();
            }
        }

        /// <summary>
        /// 将更新后的赔率配置保存到本地JSON配置文件
        ///
        /// 功能说明：
        /// 1. 将内存中的完整赔率配置序列化为JSON格式
        /// 2. 异步写入到本地配置文件中
        /// 3. 确保配置的持久化存储
        ///
        /// 文件路径：
        /// 应用程序目录/Config/Odds.json
        ///
        /// JSON格式：
        /// 使用缩进格式化，便于人工阅读和调试
        /// 结构：{游戏名称: {投注项目: {赔率配置}}}
        ///
        /// 异步操作：
        /// 使用async/await模式，避免阻塞UI线程
        /// 文件写入操作可能耗时，异步处理提升用户体验
        ///
        /// 异常处理：
        /// 捕获文件操作异常，包装为更友好的错误信息
        /// 保留原始异常信息，便于问题诊断
        ///
        /// 原子性：
        /// 文件写入操作具有原子性，要么完全成功，要么完全失败
        /// 不会出现部分写入导致的配置文件损坏
        /// </summary>
        /// <returns>异步任务对象</returns>
        /// <exception cref="Exception">文件保存失败时抛出异常</exception>
        private async Task SaveOddsToConfigFile()
        {
            try
            {
                // 将完整的赔率配置对象序列化为JSON字符串
                // 使用缩进格式化，提升可读性
                string jsonString = JsonConvert.SerializeObject(CommonHelper.LotteryGames, Formatting.Indented);

                // 构建配置文件的完整路径
                // 使用Path.Combine确保跨平台兼容性
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "Odds.json");

                // 异步写入文件，避免阻塞UI线程
                // 使用UTF-8编码确保中文字符正确保存
                await File.WriteAllTextAsync(configPath, jsonString);
            }
            catch (Exception ex)
            {
                // 包装异常信息，提供更友好的错误描述
                // 保留原始异常作为内部异常，便于调试
                throw new Exception($"保存配置文件失败：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 一键重置默认赔率按钮点击事件处理程序
        ///
        /// 功能说明：
        /// 1. 从远程服务器获取官方的默认赔率配置
        /// 2. 覆盖本地的所有赔率设置
        /// 3. 更新内存配置和界面显示
        /// 4. 提供操作确认和结果反馈
        ///
        /// 使用场景：
        /// - 赔率配置出现错误需要恢复默认值
        /// - 需要获取最新的官方赔率配置
        /// - 系统升级后需要更新赔率设置
        /// - 配置文件损坏需要重新初始化
        ///
        /// 安全机制：
        /// 1. 操作确认：显示确认对话框，防止误操作
        /// 2. 原子操作：要么完全成功，要么完全失败
        /// 3. 异常处理：捕获网络和文件操作异常
        /// 4. 日志记录：记录操作结果，便于问题追踪
        ///
        /// 执行流程：
        /// 用户确认 → 下载配置 → 保存文件 → 更新内存 → 刷新界面 → 用户反馈 → 记录日志
        ///
        /// 网络依赖：
        /// 需要能够访问RobotSetting.Current.UpdateHost指定的服务器
        /// 如果网络不可用，操作将失败并显示错误信息
        ///
        /// 数据来源：
        /// 远程服务器上的Odds.json文件，包含所有游戏的标准赔率配置
        /// </summary>
        /// <param name="sender">事件发送者（重置按钮）</param>
        /// <param name="e">事件参数</param>
        private async void uiButton_ResetOdds_Click(object sender, EventArgs e)
        {
            try
            {
                // 显示操作确认对话框
                // 防止用户误操作导致配置丢失
                DialogResult result = MessageBox.Show(
                    @"确定要重置为默认赔率吗？\n此操作将覆盖当前所有的赔率设置！",
                    @"确认重置",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                // 用户取消操作，直接返回
                if (result != DialogResult.Yes)
                {
                    return;
                }

                // 构建远程配置文件的URL地址
                // 从系统配置中获取更新服务器地址
                string url = $"{RobotSetting.Current.UpdateHost}/Odds.json";

                // 异步下载远程配置文件内容
                // 使用Flurl.Http库进行HTTP请求
                string jsonStr = await url.GetAsync().ReceiveString();

                // 将下载的配置内容保存到本地文件
                // 覆盖现有的配置文件
                await File.WriteAllTextAsync(
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "Odds.json"),
                    jsonStr);

                // 更新内存中的全局赔率配置
                // 反序列化JSON字符串为配置对象
                CommonHelper.LotteryGames = JsonConvert.DeserializeObject<LotteryGameModel>(jsonStr)!;

                // 如果当前有选中的游戏，重新加载显示数据
                // 确保界面显示的是最新的配置信息
                if (!string.IsNullOrEmpty(_currentLotteryName))
                {
                    LoadOddsDataToGrid(_currentLotteryName);
                }

                // 显示成功提示信息
                this.ShowSuccessDialog("重置默认赔率成功！");

                // 记录操作成功日志
                await DbHelper.AddLogAsync(EnumLogType.机器人, "重置赔率", "成功重置为默认赔率配置");
            }
            catch (Exception ex)
            {
                // 捕获所有可能的异常（网络异常、文件异常、JSON解析异常等）
                this.ShowErrorDialog($"重置赔率失败：{ex.Message}");

                // 记录详细的错误日志
                await DbHelper.AddLogAsync(EnumLogType.机器人, "重置赔率失败", ex.ToString());
            }
        }

        #endregion
    }
}