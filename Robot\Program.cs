using System.Net;
using System.Reflection;
using AiHelper;
using AutoUpdaterDotNET;
using Flurl.Http;
using Robot.Config;
using Robot.Enum;
using Robot.Helper;
using Robot.Ui;

namespace Robot
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点
        /// </summary>
        [STAThread]
        static void Main()
        {
            // 初始化应用程序配置，如设置高DPI设置或默认字体等
            // 详细信息请参考：https://aka.ms/applicationconfiguration
            ApplicationConfiguration.Initialize();

            // 检查是否已经运行,防止多开
            if (!SingleInstance.IsContinue())
            {
                MessageBox.Show(@"KingRobot已经在运行，请勿重复打开！");
                return;
            }

            // 设置Expect100Continue为false
            ServicePointManager.Expect100Continue = false;

            // 设置SecurityProtocol为支持的TLS版本,设置为SecurityProtocolType.SystemDefault可以让系统自动选择最佳的安全协议
            // ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
            // ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.SystemDefault;

            // 载入配置参数,确定彩种,根据不同的彩种设置更新地址
            RobotSetting.Current.Load();
            UserSetting.Current.Load();
            CommonHelper.Lottery = (EnumLottery)RobotSetting.Current.AppType;

            // 根据彩种设置更新地址
            string updateUrl = "";
            switch (CommonHelper.Lottery)
            {
                case EnumLottery.台湾宾果:
                    updateUrl = $"{RobotSetting.Current.UpdateHost}/AutoUpdater/RobotTwbg/AutoUpdater.xml";
                    break;
                case EnumLottery.一六八飞艇:
                    updateUrl = $"{RobotSetting.Current.UpdateHost}/AutoUpdater/Robot168ft/AutoUpdater.xml";
                    break;
            }

            // 异步检查更新
            try
            {
                // 异步获取服务器版本信息
                string responseContent = updateUrl.WithTimeout(TimeSpan.FromSeconds(3)).GetAsync().ReceiveString().Result;
                if (!string.IsNullOrWhiteSpace(responseContent) && responseContent.Contains("<version>"))
                {
                    string serverVersion = Ai.GetTextMiddle(responseContent, "<version>", "</version>").Trim().Replace(".", "");
                    string? locationVersion = Assembly.GetExecutingAssembly().GetName().Version?.ToString().Replace(".", "");
                    if (Convert.ToInt32(serverVersion) > Convert.ToInt32(locationVersion))
                    {
                        // 异步检查更新
                        AutoUpdater.Start(updateUrl);

                        // 给足够的时间等待更新完成
                        int sleepTime = 60 * 60 * 24 * 1000;
                        Thread.Sleep(sleepTime);
                    }
                }
            }
            catch (TimeoutException ex)
            {
                File.AppendAllText("error.log", ex.ToString());
            }
            catch (Exception ex)
            {
                File.AppendAllText("error.log", ex.ToString());
            }

            // 载入参数设置窗口,在参数设置窗口中选择对应的聊天App,并在参数设置窗口中校验授权时间
            FormLogin formIndex = new FormLogin();
            formIndex.ShowDialog();

            // 载入主窗体
            if (formIndex.DialogResult == DialogResult.OK && RegisterHelper.MyRegisterInfo.ExpireTime > CommonHelper.DateTimeNowInternet)
            {
                Application.Run(new FormMain());
            }
        }
    }
}