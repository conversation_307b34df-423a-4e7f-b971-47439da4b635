﻿using System.Globalization;
using AiHelper;
using Robot.Config;
using Robot.Constants;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;

namespace Robot.Ui;

/// <summary>
/// 显示机器人信息
/// </summary>
public partial class FormMain
{
    #region 基础信息

    /// <summary>
    /// 基础信息
    /// </summary>
    private async Task ShowInfoAsync()
    {
        try
        {
            // 头部标题及右下角信息
            Invoke(() =>
            {
                Text = $@"{RobotSetting.Current.AppName} - Ver:{Ai.中括号左}{CommonHelper.AppVersion}{Ai.中括号右} - 服务到期时间:{Ai.中括号左}{RegisterHelper.MyRegisterInfo.ExpireTime:yyyy-MM-dd HH:mm:ss}{Ai.中括号右}";
                // toolStripStatusLabel_Lottery.Text = $@"当前游戏:{Ai.中括号左}{CommonHelper.PlayGameInfo.GameName}{Ai.中括号右}";
                toolStripStatusLabel_RobotInfo.Text = $@"机器人:{Ai.中括号左}{RobotHelper.RobotInfo.Account}{Ai.小圆点}{RobotHelper.RobotInfo.NickName}{Ai.中括号右}";
            });

            // 显示封盘时间
            if (!string.IsNullOrWhiteSpace(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue))
            {
                // Invoke(() =>
                // {
                //     toolStripStatusLabel_CloseTime.Text = string.Concat($"{Ai.中括号左}{CommonHelper.Lottery}{Ai.中括号右}@ ", IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue, " 期距离封盘:", Ai.中括号左, IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery], Ai.中括号右);
                //     toolStripStatusLabel_OpenTime.Text = string.Concat("距离下期:", Ai.中括号左, IssueTimeHelper.OpenTimeDownDic[CommonHelper.Lottery], Ai.中括号右);
                // });

                // 获取开盘和封盘倒计时
                var openTimeSpan = IssueTimeHelper.OpenTimeDownDic[CommonHelper.Lottery];
                var closeTimeSpan = IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] - UserSetting.Current.封盘时间;

                // 根据状态显示不同内容
                if (closeTimeSpan > 0)
                {
                    Invoke(() =>
                    {
                        label_正在投注期数标题.Text = @"正在投注期数";
                        label_正在投注期数.Text = $@"{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}";
                        label_封盘倒计时.Text = FormatTimeRemaining(closeTimeSpan);
                    });
                }
                else
                {
                    Invoke(() =>
                    {
                        IssueTime issueTime = DbHelper.FSql.Select<IssueTime>()
                            .Where(x => x.Id.Equals(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Id + 1))
                            .ToOne();
                        label_正在投注期数标题.Text = @"即将开盘";
                        label_正在投注期数.Text = $@"{issueTime.Issue}";
                        label_封盘倒计时.Text = FormatTimeRemaining(openTimeSpan);
                    });
                }
            }

            // 判断显示最新kj信息
            KaiJiang lastKj = await DbHelper.FSql.Select<KaiJiang>()
                .OrderByDescending(a => a.Issue)
                .FirstAsync();
            if (lastKj != null)
            {
                Invoke(() => { label_开奖期数.Text = lastKj.Issue; });
                if (CommonHelper.Lottery is EnumLottery.台湾宾果)
                {
                    if (UserSetting.Current.启用台湾宾果1.Equals(1))
                    {
                        string drawNumStr = await GetDrawNumStr(lastKj, EnumBetLottery.台湾宾果1);
                        int drawNumSum = await GetDrawNumSum(drawNumStr);
                        int result = await GetDrawResult(drawNumSum);
                        Invoke(() =>
                        {
                            label_开奖号码.Text = (drawNumSum % 100).ToString();
                            label_番摊结果.Text = result.ToString();
                        });
                    }
                    else if (UserSetting.Current.启用台湾宾果2.Equals(1))
                    {
                        string drawNumStr = await GetDrawNumStr(lastKj, EnumBetLottery.台湾宾果2);
                        int drawNumSum = await GetDrawNumSum(drawNumStr);
                        int result = await GetDrawResult(drawNumSum);
                        Invoke(() =>
                        {
                            label_开奖号码.Text = (drawNumSum % 100).ToString();
                            label_番摊结果.Text = result.ToString();
                        });
                    }
                    else if (UserSetting.Current.启用台湾宾果3.Equals(1))
                    {
                        string drawNumStr = await GetDrawNumStr(lastKj, EnumBetLottery.台湾宾果3);
                        int drawNumSum = await GetDrawNumSum(drawNumStr);
                        int result = await GetDrawResult(drawNumSum);
                        Invoke(() =>
                        {
                            label_开奖号码.Text = (drawNumSum % 100).ToString();
                            label_番摊结果.Text = result.ToString();
                        });
                    }
                }
            }

            // 显示头像
            // if (!string.IsNullOrEmpty(RobotHelper.RobotInfo.AvatarUrl))
            // {
            //     Invoke(() => { uiAvatar_Robot.Image = RobotHelper.RobotInfo.Avatar; });
            // }

            // 加载群列表
            Invoke(() =>
            {
                if (comboBox_WorkGroupId.Items.Count.Equals(0) && RobotHelper.GroupDic.Any())
                {
                    comboBox_WorkGroupId.Items.Add("选择聊天群");
                    foreach (KeyValuePair<string, string> kv in RobotHelper.GroupDic)
                    {
                        comboBox_WorkGroupId.Items.Add($"{Ai.中括号左}{kv.Key}{Ai.中括号右}{kv.Value}");
                    }

                    comboBox_WorkGroupId.SelectedIndex = 0;
                }
            });

            // 判断显示飞单额度信息
            Invoke(() =>
            {
                if (!string.IsNullOrEmpty(CommonHelper.UserInfo.UserName))
                {
                    toolStripStatusLabel_MemberInfo.Visible = true;
                    toolStripStatusLabel_MemberInfo.Text = $@"飞单账号:{Ai.中括号左}{CommonHelper.UserInfo.UserName}{Ai.中括号右}可用额度:{Ai.中括号左}{CommonHelper.UserInfo.Balance}{Ai.中括号右}";
                }
                else
                {
                    toolStripStatusLabel_MemberInfo.Visible = false;
                }
            });

            // 显示今日输赢信息
            Invoke(() =>
            {
                // 计算今日所有已结算订单的总盈亏
                decimal todayWin = DbHelper.FSql.Select<BetOrder>()
                                       .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已结算)
                                       .Where(a => a.Time.Date == DateTime.Today) // 今日订单
                                       .Where(a => a.WinLose == EnumBetWinLose.中)
                                       .Sum(a => a.结算) // 中奖金额总和
                                   - DbHelper.FSql.Select<BetOrder>()
                                       .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已结算)
                                       .Where(a => a.Time.Date == DateTime.Today)
                                       .Where(a => a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂)
                                       .Sum(a => a.Money); // 减去投注金额

                toolStripStatusLabel_LastIssueWin.Text = $@"今日输赢:{Math.Round(todayWin, 2).ToString(CultureInfo.CurrentCulture)}";
            });

            // 显示当前期数投注额
            Invoke((Delegate)(() =>
            {
                decimal totalBetMoneyIssueNow = DbHelper.FSql.Select<BetOrder>()
                    .Where(a => a.Issue == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已受理 || a.BetOrderStatus == EnumBetOrderStatus.已结算)
                    .Sum(a => a.Money);

                toolStripStatusLabel_CurrentIssueBetTitle.Text = $@"当前第{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}期投注额";
                toolStripStatusLabel_CurrentIssueBet.Text = $@"{Math.Round(totalBetMoneyIssueNow, 2).ToString(CultureInfo.CurrentCulture)}";
            }));

            // 显示上期总输赢
            Invoke(() =>
            {
                // 获取上一期期号
                var lastIssue = DbHelper.FSql.Select<IssueTime>()
                    .Where(a => a.Id == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Id - 1)
                    .First();

                if (lastIssue != null)
                {
                    decimal totalWinLastIssue = DbHelper.FSql.Select<BetOrder>()
                                                    .Where(a => a.Issue == lastIssue.Issue)
                                                    .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已结算)
                                                    .Where(a => a.WinLose == EnumBetWinLose.中)
                                                    .Sum(a => a.结算) // 中奖金额总和
                                                - DbHelper.FSql.Select<BetOrder>()
                                                    .Where(a => a.Issue == lastIssue.Issue)
                                                    .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已结算)
                                                    .Where(a => a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂)
                                                    .Sum(a => a.Money); // 减去投注金额

                    toolStripStatusLabel_LastIssueWin.Text = $@"{Math.Round(totalWinLastIssue, 2).ToString(CultureInfo.CurrentCulture)}";
                }
                else
                {
                    toolStripStatusLabel_LastIssueWin.Text = @"0.00";
                }
            });

            // 显示飞单结果
            if (CommonHelper.BetSuccessIssueDic.ContainsKey(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue) && CommonHelper.BetSuccessIssueDic[IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue].Equals(false))
            {
                CommonHelper.BetSuccessIssueDic[IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue] = true;
                // Invoke(() => { this.ShowSuccessNotifier($"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}期飞单成功。", timeout: 10000); });
            }
            else if (CommonHelper.BetFailIssueDic.ContainsKey(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue) && CommonHelper.BetFailIssueDic[IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue].Equals(false))
            {
                CommonHelper.BetFailIssueDic[IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue] = true;
                // Invoke(() => { this.ShowErrorNotifier($"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}期飞单可能失败，请人工核查结果。", timeout: 10000); });
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowInfoAsyncError", ex.ToString());
        }
    }

    /// <summary>
    /// 格式化时间显示
    /// </summary>
    /// <param name="totalSeconds">总秒数</param>
    /// <returns>格式化的时间字符串</returns>
    private string FormatTimeRemaining(int totalSeconds)
    {
        if (totalSeconds <= 0)
        {
            return "00:00:00";
        }

        // 大于等于1小时
        if (totalSeconds >= 3600)
        {
            var hours = (totalSeconds / 3600).ToString("D2");
            var minutes = ((totalSeconds % 3600) / 60).ToString("D2");
            var seconds = (totalSeconds % 60).ToString("D2");
            return $"{hours}:{minutes}:{seconds}";
        }

        // 大于等于1分钟
        if (totalSeconds >= 60)
        {
            var minutes = (totalSeconds / 60).ToString("D2");
            var seconds = (totalSeconds % 60).ToString("D2");
            return $"00:{minutes}:{seconds}";
        }

        // 小于1分钟
        return $"00:00:{totalSeconds:D2}";
    }

    #endregion

    #region 显示会员信息

    private decimal MemberTotalBalance { get; set; }

    /// <summary>
    /// 加载Member信息
    /// </summary>
    private void ShowMemberInfoAsync()
    {
        Invoke(async () =>
        {
            try
            {
                // 所有会员列表
                List<Member> memberListAll = await DbHelper.FSql.Select<Member>().ToListAsync();

                // 没有数据直接返回
                if (!memberListAll.Any())
                {
                    if (dataGridView_MemberInfo.Rows.Count > 0)
                    {
                        dataGridView_MemberInfo.DataSource = null;
                    }

                    return;
                }

                // 计算总计数据
                decimal tmpTotalBalance = memberListAll.Sum(a => a.Balance);

                // 对比数据差异
                if (MemberTotalBalance.Equals(tmpTotalBalance) && !CommonHelper.NeedToRefreshMemberInfo)
                {
                    return;
                }

                // 重置状态
                MemberTotalBalance = tmpTotalBalance;
                CommonHelper.NeedToRefreshMemberInfo = false;

                // 定义临时数据源格式
                List<MemberInfo> memberInfoList = new();
                foreach (Member member in memberListAll)
                {
                    MemberInfo info = RobotHelper.GetMemberDetail(member).Result;
                    memberInfoList.Add(info);
                }

                // 将memberInfoList按照积分从高到低排序
                memberInfoList.Sort((a, b) => b.积分.CompareTo(a.积分));

                // // 插入汇总数据
                // memberInfoList.Insert(0, new MemberInfo
                // {
                //     账号 = "汇总",
                //     备注名 = "数据",
                //     积分 = Math.Round(memberInfoList.Sum(a => a.积分), 2),
                //     未结算积分 = Math.Round(memberInfoList.Sum(a => a.未结算积分), 2),
                //     总流水 = Math.Round(memberInfoList.Sum(a => a.总流水), 2),
                //     总盈亏 = Math.Round(memberInfoList.Sum(a => a.总盈亏), 2),
                //     未回流水 = Math.Round(memberInfoList.Sum(a => a.未回流水), 2),
                //     已回流水 = Math.Round(memberInfoList.Sum(a => a.已回流水), 2),
                //     回水比例 = Math.Round(UserSetting.Current.回水比例, 2),
                //     已回金额 = Math.Round(memberInfoList.Sum(a => a.已回金额), 2),
                //     总上分 = Math.Round(memberInfoList.Sum(a => a.总上分), 2),
                //     总下分 = Math.Round(memberInfoList.Sum(a => a.总下分), 2),
                //     真流水 = Math.Round(memberInfoList.Sum(a => a.真流水), 2),
                //     真盈亏 = Math.Round(memberInfoList.Sum(a => a.真盈亏), 2),
                //     假流水 = Math.Round(memberInfoList.Sum(a => a.假流水), 2),
                //     假盈亏 = Math.Round(memberInfoList.Sum(a => a.假盈亏), 2)
                // });

                // 绑定明细表格到表格
                DataGridView dgv = dataGridView_MemberInfo;
                dgv.DataSource = null;
                dgv.DataSource = memberInfoList;

                // 设置表格样式
                if (dgv.Rows.Count > 0)
                {
                    // 设置列宽
                    dgv.Columns["账号"]!.Width = 135;
                    dgv.Columns["昵称"]!.Width = 100;
                    dgv.Columns["积分"]!.Width = 80;
                    dgv.Columns["未结算积分"]!.Width = 100;
                    dgv.Columns["总流水"]!.Visible = false;
                    dgv.Columns["总盈亏"]!.Width = 80;
                    dgv.Columns["未回流水"]!.Visible = false;
                    dgv.Columns["已回流水"]!.Visible = false;
                    dgv.Columns["回水比例"]!.Width = 80;
                    dgv.Columns["已回金额"]!.Visible = false;
                    dgv.Columns["总上分"]!.Visible = false;
                    dgv.Columns["总下分"]!.Visible = false;
                    dgv.Columns["真流水"]!.Visible = false;
                    dgv.Columns["真盈亏"]!.Visible = false;
                    dgv.Columns["假流水"]!.Visible = false;
                    dgv.Columns["假盈亏"]!.Visible = false;
                    dgv.Columns["真假人"]!.Width = 70;
                    dgv.Columns["删除"]!.Width = 60;

                    // 设置对齐方式
                    dgv.Columns["账号"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns["昵称"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns["积分"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["未结算积分"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["总流水"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["总盈亏"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["未回流水"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["已回流水"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["回水比例"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns["已回金额"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["总上分"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["总下分"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["真流水"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["真盈亏"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["假流水"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["假盈亏"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["真假人"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns["删除"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

                    // 取消选中任何单元格
                    dgv.ClearSelection();

                    // 固定第1列和第2列
                    dgv.Columns[0].Frozen = dgv.Columns[1].Frozen = true;
                }

                // 显示汇总数据
                label_总人数.Text = $@"总人数:{memberInfoList.Count}";
                label_总积分.Text = $@"总积分:{Math.Round(memberInfoList.Sum(a => a.积分), 2).ToString(CultureInfo.InvariantCulture)}"; // Math.Round(memberInfoList.Sum(a => a.积分), 2).ToString(CultureInfo.InvariantCulture);
            }
            catch (Exception ex)
            {
                // Debug.WriteLine(ex);
                await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowMemberInfoAsyncError", ex.ToString());
            }
        });
    }

    #endregion

    #region 显示答题明细数据

    private List<long> BetOrderIdList { get; set; } = new();

    /// <summary>
    /// 答题数据
    /// </summary>
    private void ShowBetOrderAsync()
    {
        Invoke(async () =>
        {
            try
            {
                // 提取答题数据
                var betOrderList = await DbHelper.FSql.Select<BetOrder, Member>()
                    .InnerJoin(t => t.t1.Account == t.t2.Account)
                    .Where(t => t.t1.Issue == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    .Where(t => t.t1.BetOrderStatus == EnumBetOrderStatus.已受理)
                    .ToListAsync(t => new
                    {
                        t.t1.Id,
                        // t.t1.BetLottery,
                        期号 = t.t1.Issue,
                        t.t2.Account,
                        t.t2.备注名,
                        项目 = t.t1.Con,
                        // 赔率 = t.t1.Odds,
                        金额 = t.t1.Money,
                        飞单状态 = "飞单状态"
                    });

                // 没有数据直接返回
                if (!betOrderList.Any())
                {
                    try
                    {
                        dataGridView_BetOrderDetail.Rows.Clear();
                    }
                    catch (Exception ex)
                    {
                        // Debug.WriteLine(ex);
                        await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowBetDataAsync", ex.ToString());
                    }

                    return;
                }

                // 逐行添加数据
                bool needRefresh = false;
                foreach (var order in betOrderList)
                {
                    if (!BetOrderIdList.Contains(order.Id))
                    {
                        BetOrderIdList.Add(order.Id);

                        try
                        {
                            dataGridView_BetOrderDetail.Rows.Add(
                                order.Account,
                                order.备注名,
                                // order.BetLottery,
                                order.期号,
                                order.项目,
                                // order.赔率,
                                order.金额,
                                order.飞单状态);
                        }
                        catch (Exception ex)
                        {
                            // Debug.WriteLine(ex);
                            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowBetDataAsync", ex.ToString());
                        }

                        needRefresh = true;
                    }
                }

                // 显示到最后一行
                if (needRefresh)
                {
                    try
                    {
                        // 取消选中第一行
                        dataGridView_BetOrderDetail.Rows[0].Selected = false;

                        // 显示最后一行
                        dataGridView_BetOrderDetail.FirstDisplayedScrollingRowIndex = dataGridView_BetOrderDetail.RowCount - 1;
                    }
                    catch (Exception ex)
                    {
                        await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowBetDataAsync", ex.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                // Debug.WriteLine(ex.ToString());
                await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowBetDataAsync", ex.ToString());
            }
        });
    }

    #endregion

    #region 显示上分订单数据

    private List<long> AddMoneyOrderIdList { get; set; } = new();

    /// <summary>
    /// 上分订单数据
    /// </summary>
    private async void ShowAddMoneyOrderAsync()
    {
        try
        {
            var addMoneyOrderList = await DbHelper.FSql.Select<AddMoney, Member>()
                .InnerJoin(t => t.t1.Account == t.t2.Account)
                .Where(t => t.t1.Status == EnumBalanceStatus.等待处理)
                .ToListAsync(t => new
                {
                    t.t1.Id,
                    t.t1.Time,
                    t.t1.Account,
                    t.t2.备注名,
                    t.t1.Money,
                    t.t1.FromMsgId
                });

            // 对比数据差异
            foreach (var order in addMoneyOrderList)
            {
                if (!AddMoneyOrderIdList.Contains(order.Id))
                {
                    AddMoneyOrderIdList.Add(order.Id);
                    Invoke(() => { dataGridView_AddMoney.Rows.Add(order.Id, order.Account, order.备注名, order.Money, "同意", "拒绝"); });
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowAddMoneyOrderAsyncError", ex.ToString());
        }
    }

    #endregion

    #region 显示下分订单数据

    private List<long> SubMoneyOrderIdList { get; set; } = new();

    /// <summary>
    /// 下分订单数据
    /// </summary>
    private async void ShowSubMoneyOrderAsync()
    {
        try
        {
            var subMoneyOrderList = await DbHelper.FSql.Select<SubMoney, Member>()
                .InnerJoin(t => t.t1.Account == t.t2.Account)
                .Where(t => t.t1.Status == EnumBalanceStatus.等待处理)
                .ToListAsync(t => new
                {
                    t.t1.Id,
                    t.t1.Time,
                    t.t1.Account,
                    t.t2.备注名,
                    t.t1.Money
                });

            // 对比数据差异
            foreach (var order in subMoneyOrderList)
            {
                if (!SubMoneyOrderIdList.Contains(order.Id))
                {
                    SubMoneyOrderIdList.Add(order.Id);
                    Invoke(() => { dataGridView_SubMoney.Rows.Add(order.Id, order.Account, order.备注名, order.Money, "同意", "拒绝"); });
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowSubMoneyOrderAsyncError", ex.ToString());
        }
    }

    #endregion

    #region 显示平台日志

    private List<long> PlatformLogIdList { get; set; } = new();

    /// <summary>
    /// 显示平台日志
    /// </summary>
    private void ShowPlatformLogAsync()
    {
        Invoke(async () =>
        {
            try
            {
                // 查询Log表最后50条数据
                var logList = await DbHelper.FSql.Select<Log>()
                    .Where(t => t.Type == EnumLogType.平台)
                    .OrderByDescending(t => t.Id)
                    .Take(UiConstants.MaxLogRows)
                    .ToListAsync(a => new
                    {
                        a.Id,
                        时间 = Convert.ToDateTime(a.Time).ToString("HH:mm:ss"),
                        摘要 = a.Title,
                        详情 = a.Content
                    });

                // 没有数据直接返回
                if (!logList.Any())
                {
                    return;
                }

                // LogList倒序排列
                logList.Reverse();

                // 填充新数据
                bool needRefresh = false;
                foreach (var log in logList)
                {
                    if (!PlatformLogIdList.Contains(log.Id))
                    {
                        PlatformLogIdList.Add(log.Id);

                        try
                        {
                            dataGridView_PlatformLog.Rows.Add(log.Id, log.时间, log.摘要, log.详情);
                        }
                        catch (Exception ex)
                        {
                            // Debug.WriteLine(ex);
                            await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowPlatformLogAsync", ex.ToString());
                        }

                        needRefresh = true;
                    }
                }

                // 显示到最后一行
                if (needRefresh)
                {
                    try
                    {
                        while (dataGridView_PlatformLog.Rows.Count > UiConstants.MaxLogRows)
                        {
                            dataGridView_PlatformLog.Rows.RemoveAt(0);
                        }

                        // 取消选中第一行
                        dataGridView_PlatformLog.Rows[0].Selected = false;

                        // 显示最后一行
                        dataGridView_PlatformLog.FirstDisplayedScrollingRowIndex = dataGridView_PlatformLog.Rows.Count - 1;
                    }
                    catch (Exception ex)
                    {
                        // Debug.WriteLine(ex);
                        await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowPlatformLogAsync", ex.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                await DbHelper.AddLogAsync(EnumLogType.机器人, "ShowSubMoneyOrderAsyncError", ex.ToString());
            }
        });
    }

    #endregion

    #region 根据不同彩种提取不同的开奖号码

    /// <summary>
    /// 根据不同彩种提取不同的开奖号码
    /// </summary>
    /// <param name="kj"></param>
    /// <param name="betLottery"></param>
    /// <returns></returns>
    private static async Task<string> GetDrawNumStr(KaiJiang kj, EnumBetLottery betLottery)
    {
        string drawNumStr = "";

        try
        {
            if (betLottery == EnumBetLottery.台湾宾果1)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumStr = drawNum.Last();
            }
            else if (betLottery == EnumBetLottery.台湾宾果2)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumStr = drawNum[0] + "," + drawNum[^2] + "," + drawNum[^1];
            }
            else if (betLottery == EnumBetLottery.台湾宾果3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                for (int i = 0; i < drawNum.Length - 1; i++)
                {
                    drawNumStr += drawNum[i] + ",";
                }

                drawNumStr = drawNumStr.TrimEnd(',');
            }
            else if (betLottery == EnumBetLottery.一六八飞艇前3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumStr = drawNum[0] + "," + drawNum[1] + "," + drawNum[2];
            }
            else if (betLottery == EnumBetLottery.一六八飞艇中3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumStr = drawNum[4] + "," + drawNum[5] + "," + drawNum[6];
            }
            else if (betLottery == EnumBetLottery.一六八飞艇后3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumStr = drawNum[7] + "," + drawNum[8] + "," + drawNum[9];
            }
        }
        catch (Exception)
        {
            await Task.Delay(0);
        }

        return drawNumStr;
    }

    #endregion

    #region 根据不同彩种计算开奖号码的和

    /// <summary>
    /// 根据不同彩种计算开奖号码的和
    /// </summary>
    /// <returns></returns>
    private static async Task<int> GetDrawNumSum(string drawNumStr)
    {
        int sum = 0;

        try
        {
            string[] drawNum = Ai.Split(drawNumStr, ",");
            foreach (string num in drawNum)
            {
                sum += int.Parse(num);
            }
        }
        catch (Exception)
        {
            await Task.Delay(0);
        }

        return sum;
    }

    #endregion

    #region 根据不同彩种计算不同的开奖结果

    /// <summary>
    /// 根据不同彩种计算不同的开奖结果
    /// </summary>
    /// <returns></returns>
    private static async Task<int> GetDrawResult(int drawNumSum)
    {
        try
        {
            int drawResult = drawNumSum % 4;
            drawResult = drawResult == 0 ? 4 : drawResult;
            return drawResult;
        }
        catch (Exception)
        {
            await Task.Delay(0);
        }

        return -1;
    }

    #endregion
}