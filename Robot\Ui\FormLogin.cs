﻿using Robot.Config;
using Robot.Enum;
using Robot.Helper;
using Sunny.UI;

namespace Robot.Ui;

/// <summary>
/// 登录窗体类 - 系统启动时的授权验证和配置选择界面
///
/// 核心功能：
/// 1. 软件授权验证：检查License.txt文件中的授权码有效性
/// 2. 聊天平台选择：支持多种聊天应用平台的选择和配置
/// 3. 机器码管理：自动获取并复制机器码到剪贴板，便于授权申请
/// 4. 配置持久化：保存用户选择的聊天平台到配置文件
///
/// 业务流程：
/// 程序启动 -> 显示登录窗体 -> 选择聊天平台 -> 验证授权码 -> 进入主程序
///
/// 安全机制：
/// - 基于机器码的硬件绑定授权
/// - RSA加密的授权码验证
/// - 时间有效期检查
/// - 防止未授权使用
///
/// 支持的聊天平台：
/// - 微信系列：微信360018、微信391125等多个版本
/// - QQ系列：QQ、MyQQ、LaQQ等多个客户端
/// - 其他平台：一起聊吧等第三方聊天工具
///
/// 技术特点：
/// - 继承自Sunny.UI.UIForm，提供现代化UI界面
/// - 异步操作避免界面卡顿
/// - 完善的异常处理和用户提示
/// </summary>
public partial class FormLogin : UIForm
{
    /// <summary>
    /// 构造函数 - 初始化登录窗体并准备机器码
    ///
    /// 功能说明：
    /// 1. 初始化UI组件
    /// 2. 自动获取当前机器的唯一标识码（机器码）
    /// 3. 将机器码复制到系统剪贴板，方便用户申请授权
    ///
    /// 机器码作用：
    /// - 用于软件授权的硬件绑定
    /// - 防止授权码在不同机器间随意使用
    /// - 基于计算机系统产品UUID生成
    ///
    /// 用户体验：
    /// - 启动时自动复制机器码，用户可直接粘贴给客服申请授权
    /// - 避免用户手动查找和复制机器码的繁琐操作
    /// </summary>
    public FormLogin()
    {
        InitializeComponent();

        // 自动获取机器码并复制到剪贴板
        // 机器码是基于硬件信息生成的唯一标识，用于软件授权绑定
        // 用户可直接粘贴此机器码给客服申请授权，提升用户体验
        Clipboard.SetText(RegisterHelper.GetMachineCode());
    }

    /// <summary>
    /// 窗体加载事件处理器 - 初始化界面显示和聊天平台选项
    ///
    /// 功能详解：
    /// 1. 设置窗体标题：显示应用名称和版本号
    /// 2. 初始化聊天平台下拉框：加载所有支持的聊天应用
    /// 3. 恢复用户上次选择：从配置文件读取上次选择的平台
    ///
    /// 聊天平台说明：
    /// - 微信360018：基于千寻微信框架的微信机器人
    /// - 微信391125：支持直接Unicode表情的微信版本
    /// - QQ系列：支持多种QQ客户端和框架
    /// - 一起聊吧：第三方聊天平台支持
    ///
    /// 注释掉的平台：
    /// - 部分平台可能处于维护状态或版本兼容性问题
    /// - 可根据实际需要启用相应的聊天平台
    ///
    /// 配置持久化：
    /// - 用户选择会保存到Setting.ini配置文件
    /// - 下次启动时自动恢复上次的选择
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void FormIndex_Load(object sender, EventArgs e)
    {
        // 设置窗体标题，显示应用名称和当前版本号
        Text = $@"{RobotSetting.Current.AppName} - {CommonHelper.AppVersion}";

        // 初始化聊天应用下拉框选项
        comboBox_ChatApp.Items.Clear();

        // 添加微信系列平台
        // comboBox_ChatApp.Items.Add(EnumChatApp.微信360018); // 千寻微信框架版本
        // comboBox_ChatApp.Items.Add(EnumChatApp.微信391016); // 暂时注释的微信版本
        // comboBox_ChatApp.Items.Add(EnumChatApp.微信391125); // 主要使用的微信版本
        // comboBox_ChatApp.Items.Add(EnumChatApp.微信391216); // 暂时注释的微信版本

        // 添加QQ系列平台
        // comboBox_ChatApp.Items.Add(EnumChatApp.QQ); // 标准QQ平台
        comboBox_ChatApp.Items.Add(EnumChatApp.MyQQ); // MyQQ客户端
        // comboBox_ChatApp.Items.Add(EnumChatApp.GoQQ);      // 暂时注释的GoQQ
        // comboBox_ChatApp.Items.Add(EnumChatApp.LaQQ); // LaQQ客户端

        // 添加其他聊天平台
        comboBox_ChatApp.Items.Add(EnumChatApp.一起聊吧); // 第三方聊天平台
        // comboBox_ChatApp.Items.Add(EnumChatApp.VoceChat);  // 暂时注释的VoceChat

        // 恢复用户上次选择的聊天平台
        // 从配置文件中读取SelectChatApp索引值，实现配置持久化
        comboBox_ChatApp.SelectedIndex = UserSetting.Current.SelectChatApp;
    }

    /// <summary>
    /// 确定按钮点击事件处理器 - 执行授权验证和配置保存
    ///
    /// 核心业务流程：
    /// 1. 授权文件检查：验证License.txt文件是否存在
    /// 2. 授权码解密验证：使用RSA私钥解密授权码
    /// 3. 机器码匹配：验证授权码是否与当前机器绑定
    /// 4. 有效期检查：验证授权是否在有效期内
    /// 5. 配置保存：保存用户选择的聊天平台
    /// 6. 窗体关闭：设置DialogResult并关闭登录窗体
    ///
    /// 授权验证机制：
    /// - 基于RSA非对称加密的授权码系统
    /// - 硬件绑定：授权码与机器码强绑定
    /// - 时间限制：支持授权有效期管理
    /// - 防篡改：加密授权信息防止破解
    ///
    /// 安全特性：
    /// - 本地授权文件验证，无需联网
    /// - 机器码硬件绑定，防止授权转移
    /// - 加密授权信息，防止逆向破解
    ///
    /// 错误处理：
    /// - 文件不存在：提示联系客服获取授权
    /// - 授权无效：可能是过期或机器不匹配
    /// - 用户友好的错误提示界面
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void uiButton_Setting_Click(object sender, EventArgs e)
    {
        // 第一步：检查授权文件是否存在
        // License.txt文件包含加密的授权信息，必须存在才能继续
        if (!File.Exists("License.txt"))
        {
            this.ShowErrorDialog("授权码无效或已过期，请联系客服！");
            return;
        }

        // 第二步：读取并验证授权码
        // 从License.txt文件读取加密的授权码字符串
        RegisterHelper.RegistrationKey = File.ReadAllText("License.txt");

        // 第三步：执行授权码验证
        // ValidateRegistrationKey方法会执行以下验证：
        // - RSA解密授权码
        // - 验证机器码是否匹配
        // - 检查授权有效期
        bool isReg = RegisterHelper.ValidateRegistrationKey(RegisterHelper.RegistrationKey);
        if (!isReg)
        {
            this.ShowErrorDialog("授权码无效或已过期，请联系客服！");
            return;
        }

        // 第四步：保存用户选择的聊天平台
        // 将下拉框选中的聊天应用设置为全局配置
        CommonHelper.ChatApp = (EnumChatApp)comboBox_ChatApp.SelectedItem!;

        // 第五步：持久化配置到文件
        // 保存用户选择的聊天平台索引到配置文件
        // 下次启动时会自动恢复此选择
        UserSetting.Current.SelectChatApp = comboBox_ChatApp.SelectedIndex;
        UserSetting.Current.Save();

        // 第六步：设置窗体返回结果
        // DialogResult.OK表示登录成功，主程序将继续执行
        DialogResult = DialogResult.OK;

        // 第七步：关闭登录窗体
        // 返回到Program.cs的Main方法，继续启动主窗体
        Close();
    }
}