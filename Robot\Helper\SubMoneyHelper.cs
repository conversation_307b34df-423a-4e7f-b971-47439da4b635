﻿using AiHelper;
using FreeSql;
using Robot.ChatPlatform;
using Robot.Enum;
using Robot.Models;

namespace Robot.Helper;

public static class SubMoneyHelper
{
    /// <summary>
    /// 下分处理-同意
    /// </summary>
    /// <param name="sm"></param>
    public static async Task SubMoneyHandlerAgree(SubMoney sm)
    {
        try
        {
            // 修改申请单状态
            await DbHelper.FSql.Update<SubMoney>()
                .Set(a => a.Status, EnumBalanceStatus.同意)
                .Set(a => a.PreTime, DateTime.Now)
                .Where(a => a.Id == sm.Id)
                .ExecuteAffrowsAsync();

            // 发送群信息提醒
            await ChatHelper.SendGroupMessage($@"你申请下分{Ai.中括号左}{sm.Money}{Ai.中括号右}已处理,请查收!", sm.Account);
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "AddMoneyHandlerError", ex.ToString());
        }
    }

    /// <summary>
    /// 下分处理-拒绝
    /// </summary>
    /// <param name="sm"></param>
    public static async Task SubMoneyHandlerRefuse(SubMoney sm)
    {
        try
        {
            // 获取会员信息
            Member member = await DbHelper.FSql.Select<Member>()
                .Where(t => t.Account.Equals(sm.Account))
                .ToOneAsync();

            lock (DbHelper.DbLock)
            {
                // 使用事务操作数据库
                using DbContext dbContext = DbHelper.FSql.CreateDbContext();

                // 更新用户信息可用分值
                DbHelper.FSql.Update<Member>()
                    .Set(a => a.Balance, member.Balance + sm.Money)
                    .Where(a => a.Account == member.Account)
                    .ExecuteAffrowsAsync();

                // 修改申请单状态
                DbHelper.FSql.Update<SubMoney>()
                    .Set(a => a.Status, EnumBalanceStatus.拒绝)
                    .Set(a => a.PreTime, DateTime.Now)
                    .Where(a => a.Id == sm.Id)
                    .ExecuteAffrowsAsync();

                // 记录财务凭据
                Finance fn = new Finance
                {
                    Account = member.Account,
                    变动前 = member.Balance,
                    变动值 = sm.Money,
                    变动后 = member.Balance + sm.Money,
                    凭据 = "拒绝下分退回",
                    对应信息 = sm.FromMsgId
                };
                DbHelper.FSql.Insert<Finance>()
                    .AppendData(fn)
                    .ExecuteIdentityAsync();

                // 提交保存操作
                dbContext.SaveChangesAsync();
            }

            // 发送群信息提醒
            decimal newBalance = member.Balance + sm.Money;
            await ChatHelper.SendGroupMessage($@"下分{Ai.中括号左}{sm.Money}{Ai.中括号右}被拒绝," + "\r" + "积分已退回账户!" + "\r" + CommonHelper.GetFaceIdMoneyBag() + ":" + Math.Round(newBalance, 2), member.Account);
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "SubMoneyHandlerError", ex.ToString());
        }
    }
}