using System.ComponentModel;

namespace Robot.Models;

/// <summary>
/// 赔率显示数据模型 - 用于在FormOdds的DataGridView中显示和编辑赔率信息
/// 
/// 功能说明：
/// 1. 实现INotifyPropertyChanged接口，支持数据绑定和UI自动更新
/// 2. 提供投注项目的完整赔率配置信息
/// 3. 支持用户在界面上直接编辑赔率参数
/// 4. 用于FormOdds窗体的数据绑定和显示
/// </summary>
public class BetOptionDisplay : INotifyPropertyChanged
{
    private string _content = string.Empty;
    private decimal _odds;
    private decimal _minStake;
    private decimal _maxStake;
    private decimal _totalStake;

    /// <summary>
    /// 投注项目内容（如：1正、2正、大、小等）
    /// </summary>
    public string Content
    {
        get => _content;
        set
        {
            if (_content != value)
            {
                _content = value;
                OnPropertyChanged(nameof(Content));
            }
        }
    }

    /// <summary>
    /// 赔率 - 中奖时的赔付倍数
    /// </summary>
    public decimal Odds
    {
        get => _odds;
        set
        {
            if (_odds != value)
            {
                _odds = value;
                OnPropertyChanged(nameof(Odds));
            }
        }
    }

    /// <summary>
    /// 最低投注金额 - 单次投注的最小限制
    /// </summary>
    public decimal MinStake
    {
        get => _minStake;
        set
        {
            if (_minStake != value)
            {
                _minStake = value;
                OnPropertyChanged(nameof(MinStake));
            }
        }
    }

    /// <summary>
    /// 最高投注金额 - 单次投注的最大限制
    /// </summary>
    public decimal MaxStake
    {
        get => _maxStake;
        set
        {
            if (_maxStake != value)
            {
                _maxStake = value;
                OnPropertyChanged(nameof(MaxStake));
            }
        }
    }

    /// <summary>
    /// 总投注限额 - 该项目的总投注上限
    /// </summary>
    public decimal TotalStake
    {
        get => _totalStake;
        set
        {
            if (_totalStake != value)
            {
                _totalStake = value;
                OnPropertyChanged(nameof(TotalStake));
            }
        }
    }

    /// <summary>
    /// 属性变更通知事件
    /// </summary>
    public event PropertyChangedEventHandler? PropertyChanged;

    /// <summary>
    /// 触发属性变更通知
    /// </summary>
    /// <param name="propertyName">变更的属性名称</param>
    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    /// <summary>
    /// 从BetOption创建BetOptionDisplay实例
    /// </summary>
    /// <param name="content">投注项目内容</param>
    /// <param name="betOption">赔率配置对象</param>
    /// <returns>BetOptionDisplay实例</returns>
    public static BetOptionDisplay FromBetOption(string content, BetOption betOption)
    {
        return new BetOptionDisplay
        {
            Content = content,
            Odds = betOption.Odds,
            MinStake = betOption.MinStake,
            MaxStake = betOption.MaxStake,
            TotalStake = betOption.TotalStake
        };
    }

    /// <summary>
    /// 转换为BetOption对象
    /// </summary>
    /// <returns>BetOption实例</returns>
    public BetOption ToBetOption()
    {
        return new BetOption
        {
            Odds = this.Odds,
            MinStake = this.MinStake,
            MaxStake = this.MaxStake,
            TotalStake = this.TotalStake
        };
    }
}
