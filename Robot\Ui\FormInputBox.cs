﻿namespace Robot.Ui;

/// <summary>
/// 输入框对话窗体类
///
/// 功能概述：
/// 1. 提供一个模态对话框用于用户输入文本信息
/// 2. 支持自定义窗体标题、提示标签和默认值
/// 3. 提供确定和取消两个操作选项
/// 4. 通过DialogResult返回用户的操作结果
/// 5. 通过Value属性获取用户输入的内容
///
/// 设计特点：
/// - 模态对话框，阻塞父窗体直到用户完成操作
/// - 支持多种构造函数重载，适应不同使用场景
/// - 自动聚焦到输入框，提升用户体验
/// - 标准的确定/取消操作模式
/// - 简洁的API设计，易于使用
///
/// 使用场景：
/// - 用户信息输入（如用户名、密码等）
/// - 配置参数设置
/// - 数据修改和编辑
/// - 任何需要用户输入文本的场景
///
/// 使用示例：
/// ```csharp
/// var inputBox = new FormInputBox("设置用户名", "请输入用户名:", "默认用户");
/// if (inputBox.ShowDialog() == DialogResult.OK)
/// {
///     string userName = inputBox.Value;
///     // 处理用户输入的用户名
/// }
/// ```
/// </summary>
public partial class FormInputBox : Form
{
    #region 公共属性

    /// <summary>
    /// 用户输入的值
    ///
    /// 功能说明：
    /// 1. 在窗体显示前可以设置默认值
    /// 2. 用户点击确定后，该属性包含用户输入的最终内容
    /// 3. 用户点击取消时，该属性值不会被更新
    ///
    /// 使用方式：
    /// - 设置默认值：inputBox.Value = "默认内容";
    /// - 获取输入值：string result = inputBox.Value;
    /// </summary>
    private string Title { get; set; } = string.Empty;

    public string Value { get; private set; } = string.Empty;

    #endregion

    #region 构造函数

    /// <summary>
    /// 默认构造函数
    /// 创建一个基本输入框对话窗体，使用默认的窗体标题和提示信息
    ///
    /// 适用场景：
    /// - 简单的文本输入需求
    /// - 后续通过属性设置具体参数
    /// </summary>
    public FormInputBox()
    {
        // 初始化窗体设计器生成的组件
        InitializeComponent();
    }

    /// <summary>
    /// 带标题的构造函数
    /// 创建一个具有自定义标题的输入框对话窗体
    ///
    /// 适用场景：
    /// - 需要明确标识对话框用途的场景
    /// - 标题能够清楚说明输入内容的情况
    /// </summary>
    /// <param name="title">窗体标题，显示在窗体标题栏</param>
    public FormInputBox(string title)
    {
        // 初始化窗体组件
        InitializeComponent();
        Title = title;
    }

    /// <summary>
    /// 带标题和提示标签的构造函数
    /// 创建一个具有自定义标题和提示信息的输入框对话窗体
    ///
    /// 适用场景：
    /// - 需要向用户提供详细输入说明的场景
    /// - 标题和提示信息需要分别设置的情况
    ///
    /// 参数说明：
    /// - title: 简短的窗体标题
    /// - label: 详细的输入提示信息
    /// </summary>
    /// <param name="title">窗体标题，显示在窗体标题栏</param>
    /// <param name="label">提示标签文本，指导用户输入</param>
    public FormInputBox(string title, string label)
    {
        // 初始化窗体组件
        InitializeComponent();

        // 设置窗体标题
        Title = title;

        // 设置提示标签文本
        label_Tips.Text = label;
    }

    /// <summary>
    /// 完整参数的构造函数
    /// 创建一个具有自定义标题、提示信息和默认值的输入框对话窗体
    ///
    /// 适用场景：
    /// - 编辑现有数据的场景
    /// - 需要提供默认值供用户修改的情况
    /// - 完整配置对话框的所有显示元素
    ///
    /// 参数说明：
    /// - title: 窗体标题，通常描述操作类型
    /// - label: 提示信息，指导用户如何输入
    /// - value: 默认值，用户可以直接修改
    /// </summary>
    /// <param name="title">窗体标题，显示在窗体标题栏</param>
    /// <param name="label">提示标签文本，指导用户输入</param>
    /// <param name="value">输入框的默认值</param>
    public FormInputBox(string title, string label, string value)
    {
        // 初始化窗体组件
        InitializeComponent();

        // 设置窗体标题
        Title = title;

        // 设置提示标签文本
        label_Tips.Text = label;

        // 设置默认值（将在窗体加载时显示在输入框中）
        Value = value;
    }

    #endregion

    #region 窗体事件处理

    /// <summary>
    /// 窗体加载事件处理方法
    /// 在窗体显示时执行初始化操作，优化用户体验
    ///
    /// 执行操作：
    /// 1. 将焦点设置到输入框，用户可以直接开始输入
    /// 2. 将Value属性的值显示在输入框中
    /// 3. 如果有默认值，用户可以直接修改而不需要重新输入
    ///
    /// 用户体验优化：
    /// - 自动聚焦减少用户的鼠标点击操作
    /// - 预填充默认值提高输入效率
    /// - 符合Windows应用程序的标准行为
    /// </summary>
    /// <param name="sender">事件发送者（当前窗体）</param>
    /// <param name="e">事件参数</param>
    private void FormInputBox_Load(object sender, EventArgs e)
    {
        // 设置窗体标题
        Text = Title;

        // 将焦点设置到输入框，用户可以直接开始输入
        // 这是良好用户体验的重要细节
        textBox_Value.Focus();

        // 将预设的值显示在输入框中
        // 如果Value为空，输入框将显示为空
        // 如果Value有内容，用户可以直接修改
        textBox_Value.Text = Value;
    }

    #endregion

    #region 按钮事件处理

    /// <summary>
    /// 确定按钮点击事件处理方法
    /// 用户点击确定按钮时执行，表示用户确认输入内容
    ///
    /// 执行流程：
    /// 1. 设置对话框结果为OK，表示用户确认操作
    /// 2. 将输入框中的内容保存到Value属性
    /// 3. 关闭对话框，返回到调用代码
    ///
    /// 调用方判断：
    /// 调用方可以通过检查ShowDialog()的返回值来判断用户是否点击了确定
    /// if (inputBox.ShowDialog() == DialogResult.OK) { ... }
    ///
    /// 数据获取：
    /// 用户确认后，可以通过inputBox.Value获取用户输入的内容
    /// </summary>
    /// <param name="sender">事件发送者（确定按钮）</param>
    /// <param name="e">事件参数</param>
    private void button_Sure_Click(object sender, EventArgs e)
    {
        // 设置对话框结果为OK，表示用户确认了输入
        DialogResult = DialogResult.OK;

        // 将用户在输入框中输入的内容保存到Value属性
        // 调用方可以通过这个属性获取用户的输入
        Value = textBox_Value.Text;

        // 关闭对话框，控制权返回给调用方
        Close();
    }

    /// <summary>
    /// 取消按钮点击事件处理方法
    /// 用户点击取消按钮时执行，表示用户放弃输入操作
    ///
    /// 执行流程：
    /// 1. 设置对话框结果为Cancel，表示用户取消操作
    /// 2. 关闭对话框，不保存用户的输入内容
    /// 3. Value属性保持原有值不变
    ///
    /// 调用方判断：
    /// 调用方可以通过检查ShowDialog()的返回值来判断用户是否点击了取消
    /// if (inputBox.ShowDialog() == DialogResult.Cancel) { ... }
    ///
    /// 数据处理：
    /// 用户取消时，Value属性不会被更新，保持调用前的状态
    /// 这样可以避免意外覆盖原有数据
    /// </summary>
    /// <param name="sender">事件发送者（取消按钮）</param>
    /// <param name="e">事件参数</param>
    private void button_Cancel_Click(object sender, EventArgs e)
    {
        // 设置对话框结果为Cancel，表示用户取消了操作
        DialogResult = DialogResult.Cancel;

        // 关闭对话框，不保存任何更改
        // Value属性保持原有值不变
        Close();
    }

    #endregion
}