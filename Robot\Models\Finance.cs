﻿using FreeSql.DataAnnotations;

namespace Robot.Models;

/// <summary>
/// 财务记录模型类 - 用户资金变动的完整记录系统
///
/// 功能说明：
/// - 记录用户账户的每一笔资金变动
/// - 提供完整的财务审计追踪
/// - 支持资金流水查询和统计分析
/// - 确保财务数据的准确性和可追溯性
///
/// 业务场景：
/// - 用户上分/下分操作
/// - 投注结算进账/出账
/// - 回水返利发放
/// - 系统调账操作
/// - 其他资金变动
///
/// 审计价值：
/// - 提供完整的资金变动历史
/// - 支持财务对账和核查
/// - 便于问题追踪和客服处理
/// - 满足合规性要求
///
/// 数据库表：Finance
/// - 使用FreeSql ORM进行数据持久化
/// - 支持按用户、时间、类型查询
/// - 关联Member表进行用户管理
///
/// 设计原则：
/// - 每笔变动都有完整记录
/// - 变动前后金额必须准确
/// - 凭据信息便于追踪
/// - 时间戳确保操作顺序
/// </summary>
[Table(Name = "Finance")]
public class Finance
{
    /// <summary>
    /// 财务记录唯一标识 - 数据库自增主键
    ///
    /// 特点：
    /// - 长整型确保足够的ID空间
    /// - 自动递增，无需手动设置
    /// - 作为财务记录的唯一标识符
    /// </summary>
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 变动时间 - 资金变动发生的时间戳
    ///
    /// 用途：
    /// - 记录资金变动的准确时间
    /// - 用于财务流水的时间排序
    /// - 审计追踪的时间依据
    /// - 默认为当前系统时间
    ///
    /// 重要性：
    /// - 确保财务记录的时间准确性
    /// - 支持按时间范围查询
    /// - 便于问题定位和分析
    /// </summary>
    public DateTime 时间 { get; set; } = DateTime.Now;

    /// <summary>
    /// 用户账号 - 资金变动的用户标识
    ///
    /// 关联：
    /// - 对应Member表的Account字段
    /// - 用于用户财务记录查询
    /// - 支持按用户统计资金流水
    ///
    /// 用途：
    /// - 标识资金变动的归属用户
    /// - 支持用户个人财务查询
    /// - 便于客服处理用户问题
    /// </summary>
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 变动前余额 - 资金变动前的账户余额
    ///
    /// 作用：
    /// - 记录变动前的准确余额
    /// - 用于验证变动的正确性
    /// - 支持余额变动的完整追踪
    ///
    /// 精度：
    /// - 使用decimal类型确保精度
    /// - 通常精确到小数点后2位
    /// - 避免浮点数精度问题
    ///
    /// 验证：
    /// - 变动前 + 变动值 = 变动后
    /// - 确保财务数据的一致性
    /// </summary>
    public decimal 变动前 { get; set; }

    /// <summary>
    /// 变动金额 - 本次资金变动的具体金额
    ///
    /// 数值含义：
    /// - 正数：资金增加（上分、中奖、回水等）
    /// - 负数：资金减少（下分、投注扣款等）
    /// - 零值：特殊操作（如状态变更）
    ///
    /// 业务类型：
    /// - 上分操作：正数
    /// - 下分操作：负数
    /// - 投注扣款：负数
    /// - 中奖进账：正数
    /// - 回水返利：正数
    ///
    /// 计算公式：
    /// - 变动后余额 = 变动前余额 + 变动值
    /// </summary>
    public decimal 变动值 { get; set; }

    /// <summary>
    /// 变动后余额 - 资金变动后的账户余额
    ///
    /// 计算：
    /// - 变动后 = 变动前 + 变动值
    /// - 系统自动计算，确保准确性
    ///
    /// 验证：
    /// - 必须与Member表的Balance字段一致
    /// - 用于核对账户余额的正确性
    ///
    /// 用途：
    /// - 验证财务操作的正确性
    /// - 支持余额变动的完整追踪
    /// - 便于财务对账和审计
    /// </summary>
    public decimal 变动后 { get; set; }

    /// <summary>
    /// 操作凭据 - 资金变动的业务类型标识
    ///
    /// 常见凭据类型：
    /// - "申请上分"：用户申请增加余额
    /// - "申请下分"：用户申请提取余额
    /// - "结算进账"：投注中奖的奖金
    /// - "申请回水"：回水返利发放
    /// - "系统调账"：管理员手动调整
    /// - "投注扣款"：投注时扣除的金额
    ///
    /// 作用：
    /// - 标识资金变动的具体原因
    /// - 便于财务分类统计
    /// - 支持按业务类型查询
    /// - 提供清晰的操作记录
    /// </summary>
    public string 凭据 { get; set; } = string.Empty;

    /// <summary>
    /// 对应信息 - 资金变动的详细关联信息
    ///
    /// 内容格式：
    /// - 投注相关：消息ID + 投注内容（如："MSG123[1/100]"）
    /// - 上分下分：申请单ID（如："AddMoney_456"）
    /// - 回水相关：回水批次信息
    /// - 调账相关：调账原因说明
    ///
    /// 用途：
    /// - 提供变动的详细上下文信息
    /// - 便于追踪到具体的业务操作
    /// - 支持问题定位和客服处理
    /// - 增强财务记录的可读性
    ///
    /// 示例：
    /// - "MSG789[大/500]" - 投注大500元
    /// - "申请上分1000元" - 上分申请
    /// - "第202412期回水" - 回水发放
    /// </summary>
    public string 对应信息 { get; set; } = string.Empty;
}