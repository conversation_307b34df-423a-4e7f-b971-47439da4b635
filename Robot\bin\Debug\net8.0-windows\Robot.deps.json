{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Robot/1.0.0": {"dependencies": {"Autoupdater.NET.Official": "1.9.2", "Costura.Fody": "6.0.0", "Flurl.Http": "4.0.2", "FreeSql": "3.5.211", "FreeSql.DbContext": "3.5.211", "FreeSql.Provider.Sqlite": "3.5.211", "Newtonsoft.Json": "13.0.3", "SunnyUI": "3.8.7", "System.Management": "9.0.7", "AiHelper": "*******", "ControlHelper": "*******"}, "runtime": {"Robot.dll": {}}}, "Autoupdater.NET.Official/1.9.2": {"dependencies": {"Microsoft.Web.WebView2": "1.0.2592.51"}, "runtime": {"lib/net8.0-windows7.0/AutoUpdater.NET.dll": {"assemblyVersion": "1.9.2.0", "fileVersion": "1.9.2.0"}}}, "Costura.Fody/6.0.0": {"dependencies": {"Fody": "6.8.2"}}, "Flurl/4.0.0": {"runtime": {"lib/netstandard2.0/Flurl.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "Flurl.Http/4.0.2": {"dependencies": {"Flurl": "4.0.0"}, "runtime": {"lib/net6.0/Flurl.Http.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.0.2.0"}}}, "Fody/6.8.2": {}, "FreeSql/3.5.211": {"runtime": {"lib/netstandard2.1/FreeSql.dll": {"assemblyVersion": "3.5.211.0", "fileVersion": "3.5.211.0"}}}, "FreeSql.DbContext/3.5.211": {"dependencies": {"FreeSql": "3.5.211", "Microsoft.Extensions.DependencyInjection": "8.0.0"}, "runtime": {"lib/net8.0/FreeSql.DbContext.dll": {"assemblyVersion": "3.5.211.0", "fileVersion": "3.5.211.0"}}}, "FreeSql.Provider.Sqlite/3.5.211": {"dependencies": {"FreeSql": "3.5.211", "System.Data.SQLite.Core": "1.0.119"}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Sqlite.dll": {"assemblyVersion": "3.5.211.0", "fileVersion": "3.5.211.0"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Web.WebView2/1.0.2592.51": {"runtime": {"lib/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll": {"assemblyVersion": "1.0.2592.51", "fileVersion": "1.0.2592.51"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll": {"assemblyVersion": "1.0.2592.51", "fileVersion": "1.0.2592.51"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll": {"assemblyVersion": "1.0.2592.51", "fileVersion": "1.0.2592.51"}}, "runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.0.2592.51"}, "runtimes/win-x64/native/WebView2Loader.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.2592.51"}, "runtimes/win-x86/native/WebView2Loader.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.2592.51"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "1.0.119.0", "fileVersion": "1.0.119.0"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.119.0"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.119.0"}}}, "SunnyUI/3.8.7": {"dependencies": {"SunnyUI.Common": "3.8.7"}, "runtime": {"lib/net8.0-windows7.0/SunnyUI.dll": {"assemblyVersion": "3.8.7.0", "fileVersion": "3.8.7.0"}}}, "SunnyUI.Common/3.8.7": {"runtime": {"lib/net8.0/SunnyUI.Common.dll": {"assemblyVersion": "3.8.7.0", "fileVersion": "3.8.7.0"}}}, "System.CodeDom/9.0.7": {"runtime": {"lib/net8.0/System.CodeDom.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "System.Data.SQLite.Core/1.0.119": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.119"}}, "System.Management/9.0.7": {"dependencies": {"System.CodeDom": "9.0.7"}, "runtime": {"lib/net8.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "AiHelper/*******": {"runtime": {"AiHelper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ControlHelper/*******": {"runtime": {"ControlHelper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Robot/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Autoupdater.NET.Official/1.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-wVB0YMk5Dyc2UMUb46t5TVvzipfgkNHtuKh04wp14k/32nY7wMTz8gSMgiaX9WAHesW60K6J8uGz5G8gyGkEPQ==", "path": "autoupdater.net.official/1.9.2", "hashPath": "autoupdater.net.official.1.9.2.nupkg.sha512"}, "Costura.Fody/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Uriu9GJABMivG0wXMJs6NQ7FNE3pylir1gZEBAWDvpii3cnrmxXnOG44MMDuIVOIk/Xhef7WZFsaCNV+py9qA==", "path": "costura.fody/6.0.0", "hashPath": "costura.fody.6.0.0.nupkg.sha512"}, "Flurl/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rpts69yYgvJqg6PPgqShBQEZ4aNzWQqWpWppcT0oDWxDCIsBqiod4pj6LQZdhk+1OozLFagemldMRACdHF3CsA==", "path": "flurl/4.0.0", "hashPath": "flurl.4.0.0.nupkg.sha512"}, "Flurl.Http/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-9vCqFFyceA11yplkFD8AbCFFTvG1Lrw3tpsgOpL5sLUc28p6zcvGszNleuT6nDymRvtt5eS+rqUX+bRztg1fhA==", "path": "flurl.http/4.0.2", "hashPath": "flurl.http.4.0.2.nupkg.sha512"}, "Fody/6.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-sjGHrtGS1+kcrv99WXCvujOFBTQp4zCH3ZC9wo2LAtVaJkuLpHghQx3y4k1Q8ZKuDAbEw+HE6ZjPUJQK3ejepQ==", "path": "fody/6.8.2", "hashPath": "fody.6.8.2.nupkg.sha512"}, "FreeSql/3.5.211": {"type": "package", "serviceable": true, "sha512": "sha512-7wqfa9Wkrgjg2LB42QPUBhKOlbbYqoes3E45eZ7qELa3fErwUyVsMvWIFyaPTPW5W08LXEaECgu+Exdx33DKQw==", "path": "freesql/3.5.211", "hashPath": "freesql.3.5.211.nupkg.sha512"}, "FreeSql.DbContext/3.5.211": {"type": "package", "serviceable": true, "sha512": "sha512-cpxk9faYasqzCNek0goXy15sVOB4jrsH/ELtTCUuNGPlc/EAah40UrSqDJ/LEYTaIx7euIBmMBr5Kan9STRluA==", "path": "freesql.dbcontext/3.5.211", "hashPath": "freesql.dbcontext.3.5.211.nupkg.sha512"}, "FreeSql.Provider.Sqlite/3.5.211": {"type": "package", "serviceable": true, "sha512": "sha512-buBkpXEJ+mi/Xjq/BkoyueNKsNNLOL1VuFa1EwZHOMJmA4o2+AUL7rKkRpaU/rbspD2Enh+rOYK67wKSOLLMAQ==", "path": "freesql.provider.sqlite/3.5.211", "hashPath": "freesql.provider.sqlite.3.5.211.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.2592.51": {"type": "package", "serviceable": true, "sha512": "sha512-AC9aWCthS2JvddYA1jl4dFpLBW3GsLRInhp5dkcBzaFXsRehfoUN9olIUsrH41eNaNYd7z9NRvmy81aUA5aD1g==", "path": "microsoft.web.webview2/1.0.2592.51", "hashPath": "microsoft.web.webview2.1.0.2592.51.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512"}, "SunnyUI/3.8.7": {"type": "package", "serviceable": true, "sha512": "sha512-kwSoy+SLnRJtCplBzvLhVXsnwcMxhwBXQ9CmN4obACcaTUTaQpURv8J3RLVSbRebtDw/ew82Xj7jW1+ZvM1LmA==", "path": "sunnyui/3.8.7", "hashPath": "sunnyui.3.8.7.nupkg.sha512"}, "SunnyUI.Common/3.8.7": {"type": "package", "serviceable": true, "sha512": "sha512-c9i8sHAkhzfO6B0V96AOCgkjSqJVFcg/5rJqXCnp5f5eeawTuY7ZsrVUE13vPd84b7+gylt3lO2I1nVm6dq/Nw==", "path": "sunnyui.common/3.8.7", "hashPath": "sunnyui.common.3.8.7.nupkg.sha512"}, "System.CodeDom/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-skI5aEl6XbZelP1hZvSmzzm3mM98k22x19Zu1Lf4rmuYoFEMJr7s7Te/MWUk9twjz4utyXt3q3pYXxGxI/Y+zA==", "path": "system.codedom/9.0.7", "hashPath": "system.codedom.9.0.7.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "path": "system.data.sqlite.core/1.0.119", "hashPath": "system.data.sqlite.core.1.0.119.nupkg.sha512"}, "System.Management/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-9wHNgKnZRFLZ/vSQ7+Ai9LrKkxEKh9LvC+Ta5dwQgPsyni0ET5igPATg01WW4bx/E5Q3VtRgEGithhOXaKIh0A==", "path": "system.management/9.0.7", "hashPath": "system.management.9.0.7.nupkg.sha512"}, "AiHelper/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "ControlHelper/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}