﻿using System.ComponentModel;

namespace Robot.Ui
{
    partial class FormMain
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new Container();
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle8 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle9 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle10 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle11 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle12 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle13 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle14 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle15 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle16 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle17 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle18 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle19 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle20 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle21 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle22 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle23 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle24 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle25 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle26 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle27 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle28 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle29 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle30 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle31 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle32 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle33 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle34 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle35 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle36 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle37 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle38 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle39 = new DataGridViewCellStyle();
            ComponentResourceManager resources = new ComponentResourceManager(typeof(FormMain));
            statusStrip_Foot = new StatusStrip();
            toolStripStatusLabel_MemberInfo = new ToolStripStatusLabel();
            toolStripStatusLabel1 = new ToolStripStatusLabel();
            toolStripStatusLabel_TodayWinTitle = new ToolStripStatusLabel();
            toolStripStatusLabel_TodayWin = new ToolStripStatusLabel();
            toolStripStatusLabel2 = new ToolStripStatusLabel();
            toolStripStatusLabel_CurrentIssueBetTitle = new ToolStripStatusLabel();
            toolStripStatusLabel_CurrentIssueBet = new ToolStripStatusLabel();
            toolStripStatusLabel3 = new ToolStripStatusLabel();
            toolStripStatusLabel_LastIssueWinTitle = new ToolStripStatusLabel();
            toolStripStatusLabel_LastIssueWin = new ToolStripStatusLabel();
            toolStripStatusLabel_Lottery = new ToolStripStatusLabel();
            toolStripStatusLabel_RobotInfo = new ToolStripStatusLabel();
            tabControl_Main = new TabControl();
            tabPage_Home = new TabPage();
            tableLayoutPanel_Main = new TableLayoutPanel();
            tableLayoutPanel_Body = new TableLayoutPanel();
            tableLayoutPanel1 = new TableLayoutPanel();
            dataGridView_BetOrderDetail = new DataGridView();
            BetOrderDetail_Issue = new DataGridViewTextBoxColumn();
            BetOrderDetail_RemarkName = new DataGridViewTextBoxColumn();
            BetOrderDetail_Account = new DataGridViewTextBoxColumn();
            BetOrderDetail_Content = new DataGridViewTextBoxColumn();
            BetOrderDetail_Balance = new DataGridViewTextBoxColumn();
            BetOrderDetail_BetStatus = new DataGridViewTextBoxColumn();
            dataGridView_SubMoney = new DataGridView();
            SubMoney_Id = new DataGridViewTextBoxColumn();
            SubMoney_Account = new DataGridViewTextBoxColumn();
            SubMoney_RemarkName = new DataGridViewTextBoxColumn();
            SubMoney_Money = new DataGridViewTextBoxColumn();
            SubMoney_Agree = new DataGridViewButtonColumn();
            SubMoney_Refuse = new DataGridViewButtonColumn();
            dataGridView_AddMoney = new DataGridView();
            AddMoney_Id = new DataGridViewTextBoxColumn();
            AddMoney_Account = new DataGridViewTextBoxColumn();
            AddMoney_RemarkName = new DataGridViewTextBoxColumn();
            AddMoney_Money = new DataGridViewTextBoxColumn();
            AddMoney_Agree = new DataGridViewButtonColumn();
            AddMoney_Refuse = new DataGridViewButtonColumn();
            tableLayoutPanel_MemberAndBetOrder = new TableLayoutPanel();
            dataGridView_MemberInfo = new DataGridView();
            panel8 = new Panel();
            button_仿 = new Button();
            button_模 = new Button();
            button_版 = new Button();
            button_正 = new Button();
            button_卡奖时手动开奖或退单 = new Button();
            button_选择Q群 = new Button();
            comboBox_WorkGroupId = new ComboBox();
            panel9 = new Panel();
            label_总积分 = new Label();
            label_总人数 = new Label();
            button_撤销用户投注 = new Button();
            button_开奖历史 = new Button();
            label_番摊结果 = new Label();
            label10 = new Label();
            label_开奖号码 = new Label();
            label1 = new Label();
            label_收单状态 = new Label();
            label_开奖期数 = new Label();
            label_开奖期数标题 = new Label();
            label_封盘倒计时 = new Label();
            label_正在投注期数 = new Label();
            label_正在投注期数标题 = new Label();
            panel10 = new Panel();
            button_StopBet = new Button();
            button_StartBet = new Button();
            button_StopService = new Button();
            button_StartService = new Button();
            tabPage_BetOrderReport = new TabPage();
            tableLayoutPanel2 = new TableLayoutPanel();
            dataGridView_BetOrderReport = new DataGridView();
            panel1 = new Panel();
            comboBox_SelectMemberForBetOrder = new ComboBox();
            label4 = new Label();
            comboBox_SelectIssueForBetOrder = new ComboBox();
            label3 = new Label();
            tabPage_BetTotalReport = new TabPage();
            tableLayoutPanel3 = new TableLayoutPanel();
            dataGridView_HuiZongReport = new DataGridView();
            panel2 = new Panel();
            comboBox_SelectIssueForHuiZong = new ComboBox();
            label6 = new Label();
            tabPage_AddMoneyReport = new TabPage();
            tableLayoutPanel4 = new TableLayoutPanel();
            dataGridView_AddMoneyReport = new DataGridView();
            panel3 = new Panel();
            comboBox_SelectObjForAddMoneyReport = new ComboBox();
            label7 = new Label();
            tabPage_SubMoneyReport = new TabPage();
            tableLayoutPanel5 = new TableLayoutPanel();
            dataGridView_SubMoneyReport = new DataGridView();
            panel4 = new Panel();
            comboBox_SelectObjForSubMoneyReport = new ComboBox();
            label8 = new Label();
            tabPage_FinanceReport = new TabPage();
            tableLayoutPanel6 = new TableLayoutPanel();
            dataGridView_FinanceReport = new DataGridView();
            panel5 = new Panel();
            comboBox_SelectObjForFinanceReport = new ComboBox();
            label5 = new Label();
            tabPage_LaShou = new TabPage();
            tableLayoutPanel9 = new TableLayoutPanel();
            dataGridView_LaShou = new DataGridView();
            panel7 = new Panel();
            label_LaShouTips = new Label();
            uiButton_JieSuanLaShou = new Sunny.UI.UIButton();
            uiButton_CheckLaShou = new Sunny.UI.UIButton();
            tabPage_RecMsg = new TabPage();
            tableLayoutPanel7 = new TableLayoutPanel();
            dataGridView_RecMsg = new DataGridView();
            panel6 = new Panel();
            comboBox_SelectMemberForShowRecMsg = new ComboBox();
            label9 = new Label();
            tabPage_Setting = new TabPage();
            tableLayoutPanel8 = new TableLayoutPanel();
            panel_Setting = new Panel();
            dataGridView_PlatformLog = new DataGridView();
            PlatformLog_Id = new DataGridViewTextBoxColumn();
            PlatformLog_Time = new DataGridViewTextBoxColumn();
            PlatformLog_Title = new DataGridViewTextBoxColumn();
            PlatformLog_Content = new DataGridViewTextBoxColumn();
            uiButton_ShowTime = new Sunny.UI.UIButton();
            groupBox4 = new GroupBox();
            uiButton_Odds = new Sunny.UI.UIButton();
            groupBox8 = new GroupBox();
            checkBox_AutoHuiShui = new CheckBox();
            checkBox_IsDuiChong = new CheckBox();
            checkBox_JiaRenAutoAddMoney = new CheckBox();
            groupBox6 = new GroupBox();
            label2 = new Label();
            comboBox_CloseTime = new ComboBox();
            groupBox5 = new GroupBox();
            uiButton_Clear = new Sunny.UI.UIButton();
            checkBox_SaveMemberBalance = new CheckBox();
            checkBox_SaveMemberBaseInfo = new CheckBox();
            groupBox3 = new GroupBox();
            radioButton_ImgType2 = new RadioButton();
            radioButton_ImgType1 = new RadioButton();
            checkBox_6Rows = new CheckBox();
            checkBox_7Rows = new CheckBox();
            groupBox2 = new GroupBox();
            uiButton_CancelBetData = new Sunny.UI.UIButton();
            textBox_CancelIssue = new TextBox();
            groupBox1 = new GroupBox();
            uiButton_OneKeyRebate = new Sunny.UI.UIButton();
            uiButton_SaveReturnCommissionPercent = new Sunny.UI.UIButton();
            checkBox_ShowReturnCommissionDetail = new CheckBox();
            textBox_ReturnCommissionPercent = new TextBox();
            tabPage_Log = new TabPage();
            dataGridView_Log = new DataGridView();
            Log_Id = new DataGridViewTextBoxColumn();
            Log_Type = new DataGridViewTextBoxColumn();
            Log_Time = new DataGridViewTextBoxColumn();
            Log_Title = new DataGridViewTextBoxColumn();
            Log_Content = new DataGridViewTextBoxColumn();
            contextMenuStrip_Menu = new ContextMenuStrip(components);
            notifyIcon_Pass = new NotifyIcon(components);
            notifyIcon_NG = new NotifyIcon(components);
            statusStrip_Foot.SuspendLayout();
            tabControl_Main.SuspendLayout();
            tabPage_Home.SuspendLayout();
            tableLayoutPanel_Main.SuspendLayout();
            tableLayoutPanel_Body.SuspendLayout();
            tableLayoutPanel1.SuspendLayout();
            ((ISupportInitialize)dataGridView_BetOrderDetail).BeginInit();
            ((ISupportInitialize)dataGridView_SubMoney).BeginInit();
            ((ISupportInitialize)dataGridView_AddMoney).BeginInit();
            tableLayoutPanel_MemberAndBetOrder.SuspendLayout();
            ((ISupportInitialize)dataGridView_MemberInfo).BeginInit();
            panel8.SuspendLayout();
            panel9.SuspendLayout();
            panel10.SuspendLayout();
            tabPage_BetOrderReport.SuspendLayout();
            tableLayoutPanel2.SuspendLayout();
            ((ISupportInitialize)dataGridView_BetOrderReport).BeginInit();
            panel1.SuspendLayout();
            tabPage_BetTotalReport.SuspendLayout();
            tableLayoutPanel3.SuspendLayout();
            ((ISupportInitialize)dataGridView_HuiZongReport).BeginInit();
            panel2.SuspendLayout();
            tabPage_AddMoneyReport.SuspendLayout();
            tableLayoutPanel4.SuspendLayout();
            ((ISupportInitialize)dataGridView_AddMoneyReport).BeginInit();
            panel3.SuspendLayout();
            tabPage_SubMoneyReport.SuspendLayout();
            tableLayoutPanel5.SuspendLayout();
            ((ISupportInitialize)dataGridView_SubMoneyReport).BeginInit();
            panel4.SuspendLayout();
            tabPage_FinanceReport.SuspendLayout();
            tableLayoutPanel6.SuspendLayout();
            ((ISupportInitialize)dataGridView_FinanceReport).BeginInit();
            panel5.SuspendLayout();
            tabPage_LaShou.SuspendLayout();
            tableLayoutPanel9.SuspendLayout();
            ((ISupportInitialize)dataGridView_LaShou).BeginInit();
            panel7.SuspendLayout();
            tabPage_RecMsg.SuspendLayout();
            tableLayoutPanel7.SuspendLayout();
            ((ISupportInitialize)dataGridView_RecMsg).BeginInit();
            panel6.SuspendLayout();
            tabPage_Setting.SuspendLayout();
            tableLayoutPanel8.SuspendLayout();
            panel_Setting.SuspendLayout();
            ((ISupportInitialize)dataGridView_PlatformLog).BeginInit();
            groupBox4.SuspendLayout();
            groupBox8.SuspendLayout();
            groupBox6.SuspendLayout();
            groupBox5.SuspendLayout();
            groupBox3.SuspendLayout();
            groupBox2.SuspendLayout();
            groupBox1.SuspendLayout();
            tabPage_Log.SuspendLayout();
            ((ISupportInitialize)dataGridView_Log).BeginInit();
            SuspendLayout();
            // 
            // statusStrip_Foot
            // 
            statusStrip_Foot.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
            statusStrip_Foot.Items.AddRange(new ToolStripItem[] { toolStripStatusLabel_MemberInfo, toolStripStatusLabel1, toolStripStatusLabel_TodayWinTitle, toolStripStatusLabel_TodayWin, toolStripStatusLabel2, toolStripStatusLabel_CurrentIssueBetTitle, toolStripStatusLabel_CurrentIssueBet, toolStripStatusLabel3, toolStripStatusLabel_LastIssueWinTitle, toolStripStatusLabel_LastIssueWin, toolStripStatusLabel_Lottery, toolStripStatusLabel_RobotInfo });
            statusStrip_Foot.Location = new Point(0, 708);
            statusStrip_Foot.Name = "statusStrip_Foot";
            statusStrip_Foot.Size = new Size(1283, 29);
            statusStrip_Foot.TabIndex = 0;
            statusStrip_Foot.Text = "statusStrip1";
            // 
            // toolStripStatusLabel_MemberInfo
            // 
            toolStripStatusLabel_MemberInfo.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel_MemberInfo.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            toolStripStatusLabel_MemberInfo.ForeColor = Color.Red;
            toolStripStatusLabel_MemberInfo.Name = "toolStripStatusLabel_MemberInfo";
            toolStripStatusLabel_MemberInfo.Size = new Size(88, 24);
            toolStripStatusLabel_MemberInfo.Text = "MemberInfo";
            // 
            // toolStripStatusLabel1
            // 
            toolStripStatusLabel1.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            toolStripStatusLabel1.Size = new Size(197, 24);
            toolStripStatusLabel1.Spring = true;
            // 
            // toolStripStatusLabel_TodayWinTitle
            // 
            toolStripStatusLabel_TodayWinTitle.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel_TodayWinTitle.Name = "toolStripStatusLabel_TodayWinTitle";
            toolStripStatusLabel_TodayWinTitle.Size = new Size(69, 24);
            toolStripStatusLabel_TodayWinTitle.Text = "今日输赢";
            // 
            // toolStripStatusLabel_TodayWin
            // 
            toolStripStatusLabel_TodayWin.Name = "toolStripStatusLabel_TodayWin";
            toolStripStatusLabel_TodayWin.Size = new Size(17, 24);
            toolStripStatusLabel_TodayWin.Text = "0";
            // 
            // toolStripStatusLabel2
            // 
            toolStripStatusLabel2.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel2.Name = "toolStripStatusLabel2";
            toolStripStatusLabel2.Size = new Size(197, 24);
            toolStripStatusLabel2.Spring = true;
            // 
            // toolStripStatusLabel_CurrentIssueBetTitle
            // 
            toolStripStatusLabel_CurrentIssueBetTitle.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel_CurrentIssueBetTitle.Name = "toolStripStatusLabel_CurrentIssueBetTitle";
            toolStripStatusLabel_CurrentIssueBetTitle.Size = new Size(118, 24);
            toolStripStatusLabel_CurrentIssueBetTitle.Text = "当前第x期投注额";
            // 
            // toolStripStatusLabel_CurrentIssueBet
            // 
            toolStripStatusLabel_CurrentIssueBet.Name = "toolStripStatusLabel_CurrentIssueBet";
            toolStripStatusLabel_CurrentIssueBet.Size = new Size(17, 24);
            toolStripStatusLabel_CurrentIssueBet.Text = "0";
            // 
            // toolStripStatusLabel3
            // 
            toolStripStatusLabel3.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel3.Name = "toolStripStatusLabel3";
            toolStripStatusLabel3.Size = new Size(197, 24);
            toolStripStatusLabel3.Spring = true;
            // 
            // toolStripStatusLabel_LastIssueWinTitle
            // 
            toolStripStatusLabel_LastIssueWinTitle.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel_LastIssueWinTitle.Name = "toolStripStatusLabel_LastIssueWinTitle";
            toolStripStatusLabel_LastIssueWinTitle.Size = new Size(83, 24);
            toolStripStatusLabel_LastIssueWinTitle.Text = "上期总输赢";
            // 
            // toolStripStatusLabel_LastIssueWin
            // 
            toolStripStatusLabel_LastIssueWin.Name = "toolStripStatusLabel_LastIssueWin";
            toolStripStatusLabel_LastIssueWin.Size = new Size(17, 24);
            toolStripStatusLabel_LastIssueWin.Text = "0";
            // 
            // toolStripStatusLabel_Lottery
            // 
            toolStripStatusLabel_Lottery.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel_Lottery.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            toolStripStatusLabel_Lottery.ForeColor = Color.Red;
            toolStripStatusLabel_Lottery.Name = "toolStripStatusLabel_Lottery";
            toolStripStatusLabel_Lottery.Size = new Size(197, 24);
            toolStripStatusLabel_Lottery.Spring = true;
            // 
            // toolStripStatusLabel_RobotInfo
            // 
            toolStripStatusLabel_RobotInfo.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            toolStripStatusLabel_RobotInfo.ForeColor = Color.Red;
            toolStripStatusLabel_RobotInfo.Name = "toolStripStatusLabel_RobotInfo";
            toolStripStatusLabel_RobotInfo.Size = new Size(70, 24);
            toolStripStatusLabel_RobotInfo.Text = "RobotInfo";
            // 
            // tabControl_Main
            // 
            tabControl_Main.Controls.Add(tabPage_Home);
            tabControl_Main.Controls.Add(tabPage_BetOrderReport);
            tabControl_Main.Controls.Add(tabPage_BetTotalReport);
            tabControl_Main.Controls.Add(tabPage_AddMoneyReport);
            tabControl_Main.Controls.Add(tabPage_SubMoneyReport);
            tabControl_Main.Controls.Add(tabPage_FinanceReport);
            tabControl_Main.Controls.Add(tabPage_LaShou);
            tabControl_Main.Controls.Add(tabPage_RecMsg);
            tabControl_Main.Controls.Add(tabPage_Setting);
            tabControl_Main.Controls.Add(tabPage_Log);
            tabControl_Main.Dock = DockStyle.Fill;
            tabControl_Main.Location = new Point(0, 0);
            tabControl_Main.Name = "tabControl_Main";
            tabControl_Main.SelectedIndex = 0;
            tabControl_Main.Size = new Size(1283, 708);
            tabControl_Main.TabIndex = 1;
            tabControl_Main.SelectedIndexChanged += tabControl_Main_SelectedIndexChanged;
            // 
            // tabPage_Home
            // 
            tabPage_Home.Controls.Add(tableLayoutPanel_Main);
            tabPage_Home.Location = new Point(4, 26);
            tabPage_Home.Name = "tabPage_Home";
            tabPage_Home.Size = new Size(1275, 678);
            tabPage_Home.TabIndex = 0;
            tabPage_Home.Text = "▶主控台";
            tabPage_Home.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel_Main
            // 
            tableLayoutPanel_Main.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel_Main.ColumnCount = 1;
            tableLayoutPanel_Main.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Main.Controls.Add(tableLayoutPanel_Body, 0, 0);
            tableLayoutPanel_Main.Dock = DockStyle.Fill;
            tableLayoutPanel_Main.Location = new Point(0, 0);
            tableLayoutPanel_Main.Margin = new Padding(0);
            tableLayoutPanel_Main.Name = "tableLayoutPanel_Main";
            tableLayoutPanel_Main.RowCount = 1;
            tableLayoutPanel_Main.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Main.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            tableLayoutPanel_Main.Size = new Size(1275, 678);
            tableLayoutPanel_Main.TabIndex = 1;
            // 
            // tableLayoutPanel_Body
            // 
            tableLayoutPanel_Body.ColumnCount = 2;
            tableLayoutPanel_Body.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Body.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 500F));
            tableLayoutPanel_Body.Controls.Add(tableLayoutPanel1, 1, 0);
            tableLayoutPanel_Body.Controls.Add(tableLayoutPanel_MemberAndBetOrder, 0, 0);
            tableLayoutPanel_Body.Dock = DockStyle.Fill;
            tableLayoutPanel_Body.Location = new Point(1, 1);
            tableLayoutPanel_Body.Margin = new Padding(0);
            tableLayoutPanel_Body.Name = "tableLayoutPanel_Body";
            tableLayoutPanel_Body.RowCount = 1;
            tableLayoutPanel_Body.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Body.Size = new Size(1273, 676);
            tableLayoutPanel_Body.TabIndex = 0;
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.ColumnCount = 1;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.Controls.Add(dataGridView_BetOrderDetail, 0, 2);
            tableLayoutPanel1.Controls.Add(dataGridView_SubMoney, 0, 1);
            tableLayoutPanel1.Controls.Add(dataGridView_AddMoney, 0, 0);
            tableLayoutPanel1.Dock = DockStyle.Fill;
            tableLayoutPanel1.Location = new Point(773, 0);
            tableLayoutPanel1.Margin = new Padding(0);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 3;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 15F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 15F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 70F));
            tableLayoutPanel1.Size = new Size(500, 676);
            tableLayoutPanel1.TabIndex = 2;
            // 
            // dataGridView_BetOrderDetail
            // 
            dataGridView_BetOrderDetail.AllowUserToAddRows = false;
            dataGridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = SystemColors.Control;
            dataGridViewCellStyle1.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle1.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = DataGridViewTriState.True;
            dataGridView_BetOrderDetail.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            dataGridView_BetOrderDetail.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView_BetOrderDetail.Columns.AddRange(new DataGridViewColumn[] { BetOrderDetail_Issue, BetOrderDetail_RemarkName, BetOrderDetail_Account, BetOrderDetail_Content, BetOrderDetail_Balance, BetOrderDetail_BetStatus });
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = SystemColors.Window;
            dataGridViewCellStyle2.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle2.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.False;
            dataGridView_BetOrderDetail.DefaultCellStyle = dataGridViewCellStyle2;
            dataGridView_BetOrderDetail.Dock = DockStyle.Fill;
            dataGridView_BetOrderDetail.Location = new Point(3, 205);
            dataGridView_BetOrderDetail.Name = "dataGridView_BetOrderDetail";
            dataGridView_BetOrderDetail.ReadOnly = true;
            dataGridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = SystemColors.Control;
            dataGridViewCellStyle3.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle3.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle3.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = DataGridViewTriState.True;
            dataGridView_BetOrderDetail.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            dataGridView_BetOrderDetail.Size = new Size(494, 468);
            dataGridView_BetOrderDetail.TabIndex = 3;
            // 
            // BetOrderDetail_Issue
            // 
            BetOrderDetail_Issue.HeaderText = "期号";
            BetOrderDetail_Issue.Name = "BetOrderDetail_Issue";
            BetOrderDetail_Issue.ReadOnly = true;
            // 
            // BetOrderDetail_RemarkName
            // 
            BetOrderDetail_RemarkName.HeaderText = "备注名";
            BetOrderDetail_RemarkName.Name = "BetOrderDetail_RemarkName";
            BetOrderDetail_RemarkName.ReadOnly = true;
            // 
            // BetOrderDetail_Account
            // 
            BetOrderDetail_Account.HeaderText = "账号";
            BetOrderDetail_Account.Name = "BetOrderDetail_Account";
            BetOrderDetail_Account.ReadOnly = true;
            // 
            // BetOrderDetail_Content
            // 
            BetOrderDetail_Content.HeaderText = "项目";
            BetOrderDetail_Content.Name = "BetOrderDetail_Content";
            BetOrderDetail_Content.ReadOnly = true;
            BetOrderDetail_Content.Width = 80;
            // 
            // BetOrderDetail_Balance
            // 
            BetOrderDetail_Balance.HeaderText = "金额";
            BetOrderDetail_Balance.Name = "BetOrderDetail_Balance";
            BetOrderDetail_Balance.ReadOnly = true;
            BetOrderDetail_Balance.Width = 80;
            // 
            // BetOrderDetail_BetStatus
            // 
            BetOrderDetail_BetStatus.HeaderText = "飞单状态";
            BetOrderDetail_BetStatus.Name = "BetOrderDetail_BetStatus";
            BetOrderDetail_BetStatus.ReadOnly = true;
            // 
            // dataGridView_SubMoney
            // 
            dataGridView_SubMoney.AllowUserToAddRows = false;
            dataGridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = SystemColors.Control;
            dataGridViewCellStyle4.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle4.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle4.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle4.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = DataGridViewTriState.True;
            dataGridView_SubMoney.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle4;
            dataGridView_SubMoney.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView_SubMoney.Columns.AddRange(new DataGridViewColumn[] { SubMoney_Id, SubMoney_Account, SubMoney_RemarkName, SubMoney_Money, SubMoney_Agree, SubMoney_Refuse });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = SystemColors.Window;
            dataGridViewCellStyle5.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle5.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle5.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle5.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            dataGridView_SubMoney.DefaultCellStyle = dataGridViewCellStyle5;
            dataGridView_SubMoney.Dock = DockStyle.Fill;
            dataGridView_SubMoney.Location = new Point(3, 104);
            dataGridView_SubMoney.Name = "dataGridView_SubMoney";
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = SystemColors.Control;
            dataGridViewCellStyle6.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle6.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle6.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            dataGridView_SubMoney.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            dataGridView_SubMoney.Size = new Size(494, 95);
            dataGridView_SubMoney.TabIndex = 2;
            // 
            // SubMoney_Id
            // 
            SubMoney_Id.HeaderText = "Id";
            SubMoney_Id.Name = "SubMoney_Id";
            // 
            // SubMoney_Account
            // 
            SubMoney_Account.HeaderText = "账号";
            SubMoney_Account.Name = "SubMoney_Account";
            // 
            // SubMoney_RemarkName
            // 
            SubMoney_RemarkName.HeaderText = "备注名";
            SubMoney_RemarkName.Name = "SubMoney_RemarkName";
            // 
            // SubMoney_Money
            // 
            SubMoney_Money.HeaderText = "下分";
            SubMoney_Money.Name = "SubMoney_Money";
            // 
            // SubMoney_Agree
            // 
            SubMoney_Agree.HeaderText = "接受下分";
            SubMoney_Agree.Name = "SubMoney_Agree";
            SubMoney_Agree.Resizable = DataGridViewTriState.True;
            SubMoney_Agree.SortMode = DataGridViewColumnSortMode.Automatic;
            SubMoney_Agree.Text = "接受";
            // 
            // SubMoney_Refuse
            // 
            SubMoney_Refuse.HeaderText = "拒绝下分";
            SubMoney_Refuse.Name = "SubMoney_Refuse";
            SubMoney_Refuse.Resizable = DataGridViewTriState.True;
            SubMoney_Refuse.SortMode = DataGridViewColumnSortMode.Automatic;
            SubMoney_Refuse.Text = "拒绝";
            // 
            // dataGridView_AddMoney
            // 
            dataGridView_AddMoney.AllowUserToAddRows = false;
            dataGridViewCellStyle7.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle7.BackColor = SystemColors.Control;
            dataGridViewCellStyle7.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle7.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle7.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle7.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = DataGridViewTriState.True;
            dataGridView_AddMoney.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle7;
            dataGridView_AddMoney.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView_AddMoney.Columns.AddRange(new DataGridViewColumn[] { AddMoney_Id, AddMoney_Account, AddMoney_RemarkName, AddMoney_Money, AddMoney_Agree, AddMoney_Refuse });
            dataGridViewCellStyle8.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle8.BackColor = SystemColors.Window;
            dataGridViewCellStyle8.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle8.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle8.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle8.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle8.WrapMode = DataGridViewTriState.False;
            dataGridView_AddMoney.DefaultCellStyle = dataGridViewCellStyle8;
            dataGridView_AddMoney.Dock = DockStyle.Fill;
            dataGridView_AddMoney.Location = new Point(3, 3);
            dataGridView_AddMoney.Name = "dataGridView_AddMoney";
            dataGridViewCellStyle9.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle9.BackColor = SystemColors.Control;
            dataGridViewCellStyle9.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle9.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle9.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle9.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle9.WrapMode = DataGridViewTriState.True;
            dataGridView_AddMoney.RowHeadersDefaultCellStyle = dataGridViewCellStyle9;
            dataGridView_AddMoney.Size = new Size(494, 95);
            dataGridView_AddMoney.TabIndex = 1;
            // 
            // AddMoney_Id
            // 
            AddMoney_Id.HeaderText = "Id";
            AddMoney_Id.Name = "AddMoney_Id";
            // 
            // AddMoney_Account
            // 
            AddMoney_Account.HeaderText = "账号";
            AddMoney_Account.Name = "AddMoney_Account";
            // 
            // AddMoney_RemarkName
            // 
            AddMoney_RemarkName.HeaderText = "备注名";
            AddMoney_RemarkName.Name = "AddMoney_RemarkName";
            // 
            // AddMoney_Money
            // 
            AddMoney_Money.HeaderText = "上分";
            AddMoney_Money.Name = "AddMoney_Money";
            // 
            // AddMoney_Agree
            // 
            AddMoney_Agree.HeaderText = "接受上分";
            AddMoney_Agree.Name = "AddMoney_Agree";
            AddMoney_Agree.Resizable = DataGridViewTriState.True;
            AddMoney_Agree.SortMode = DataGridViewColumnSortMode.Automatic;
            AddMoney_Agree.Text = "接受";
            // 
            // AddMoney_Refuse
            // 
            AddMoney_Refuse.HeaderText = "拒绝上分";
            AddMoney_Refuse.Name = "AddMoney_Refuse";
            AddMoney_Refuse.Resizable = DataGridViewTriState.True;
            AddMoney_Refuse.SortMode = DataGridViewColumnSortMode.Automatic;
            AddMoney_Refuse.Text = "拒绝";
            // 
            // tableLayoutPanel_MemberAndBetOrder
            // 
            tableLayoutPanel_MemberAndBetOrder.ColumnCount = 1;
            tableLayoutPanel_MemberAndBetOrder.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel_MemberAndBetOrder.Controls.Add(dataGridView_MemberInfo, 0, 2);
            tableLayoutPanel_MemberAndBetOrder.Controls.Add(panel8, 0, 0);
            tableLayoutPanel_MemberAndBetOrder.Controls.Add(panel9, 0, 1);
            tableLayoutPanel_MemberAndBetOrder.Controls.Add(panel10, 0, 3);
            tableLayoutPanel_MemberAndBetOrder.Dock = DockStyle.Fill;
            tableLayoutPanel_MemberAndBetOrder.Location = new Point(0, 0);
            tableLayoutPanel_MemberAndBetOrder.Margin = new Padding(0);
            tableLayoutPanel_MemberAndBetOrder.Name = "tableLayoutPanel_MemberAndBetOrder";
            tableLayoutPanel_MemberAndBetOrder.RowCount = 4;
            tableLayoutPanel_MemberAndBetOrder.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel_MemberAndBetOrder.RowStyles.Add(new RowStyle(SizeType.Absolute, 100F));
            tableLayoutPanel_MemberAndBetOrder.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel_MemberAndBetOrder.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel_MemberAndBetOrder.Size = new Size(773, 676);
            tableLayoutPanel_MemberAndBetOrder.TabIndex = 1;
            // 
            // dataGridView_MemberInfo
            // 
            dataGridView_MemberInfo.AllowUserToAddRows = false;
            dataGridViewCellStyle10.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle10.BackColor = SystemColors.Control;
            dataGridViewCellStyle10.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle10.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle10.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle10.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle10.WrapMode = DataGridViewTriState.True;
            dataGridView_MemberInfo.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle10;
            dataGridView_MemberInfo.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle11.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle11.BackColor = SystemColors.Window;
            dataGridViewCellStyle11.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle11.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle11.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle11.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle11.WrapMode = DataGridViewTriState.False;
            dataGridView_MemberInfo.DefaultCellStyle = dataGridViewCellStyle11;
            dataGridView_MemberInfo.Dock = DockStyle.Fill;
            dataGridView_MemberInfo.Location = new Point(3, 138);
            dataGridView_MemberInfo.Name = "dataGridView_MemberInfo";
            dataGridViewCellStyle12.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle12.BackColor = SystemColors.Control;
            dataGridViewCellStyle12.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle12.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle12.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle12.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle12.WrapMode = DataGridViewTriState.True;
            dataGridView_MemberInfo.RowHeadersDefaultCellStyle = dataGridViewCellStyle12;
            dataGridView_MemberInfo.Size = new Size(767, 500);
            dataGridView_MemberInfo.TabIndex = 2;
            dataGridView_MemberInfo.VirtualMode = true;
            // 
            // panel8
            // 
            panel8.Controls.Add(button_仿);
            panel8.Controls.Add(button_模);
            panel8.Controls.Add(button_版);
            panel8.Controls.Add(button_正);
            panel8.Controls.Add(button_卡奖时手动开奖或退单);
            panel8.Controls.Add(button_选择Q群);
            panel8.Controls.Add(comboBox_WorkGroupId);
            panel8.Dock = DockStyle.Fill;
            panel8.Location = new Point(0, 0);
            panel8.Margin = new Padding(0);
            panel8.Name = "panel8";
            panel8.Size = new Size(773, 35);
            panel8.TabIndex = 0;
            // 
            // button_仿
            // 
            button_仿.Font = new Font("宋体", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 134);
            button_仿.ForeColor = Color.Red;
            button_仿.Location = new Point(578, 2);
            button_仿.Name = "button_仿";
            button_仿.Size = new Size(45, 30);
            button_仿.TabIndex = 12;
            button_仿.Text = "仿";
            button_仿.UseVisualStyleBackColor = true;
            // 
            // button_模
            // 
            button_模.Font = new Font("宋体", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 134);
            button_模.ForeColor = Color.Red;
            button_模.Location = new Point(534, 2);
            button_模.Name = "button_模";
            button_模.Size = new Size(45, 30);
            button_模.TabIndex = 11;
            button_模.Text = "模";
            button_模.UseVisualStyleBackColor = true;
            // 
            // button_版
            // 
            button_版.Font = new Font("宋体", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 134);
            button_版.ForeColor = Color.Red;
            button_版.Location = new Point(490, 2);
            button_版.Name = "button_版";
            button_版.Size = new Size(45, 30);
            button_版.TabIndex = 10;
            button_版.Text = "版";
            button_版.UseVisualStyleBackColor = true;
            // 
            // button_正
            // 
            button_正.Font = new Font("宋体", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 134);
            button_正.ForeColor = Color.Red;
            button_正.Location = new Point(446, 2);
            button_正.Name = "button_正";
            button_正.Size = new Size(45, 30);
            button_正.TabIndex = 9;
            button_正.Text = "正";
            button_正.UseVisualStyleBackColor = true;
            // 
            // button_卡奖时手动开奖或退单
            // 
            button_卡奖时手动开奖或退单.Location = new Point(299, 3);
            button_卡奖时手动开奖或退单.Name = "button_卡奖时手动开奖或退单";
            button_卡奖时手动开奖或退单.Size = new Size(138, 28);
            button_卡奖时手动开奖或退单.TabIndex = 8;
            button_卡奖时手动开奖或退单.Text = "卡奖时手动开奖或退单";
            button_卡奖时手动开奖或退单.UseVisualStyleBackColor = true;
            // 
            // button_选择Q群
            // 
            button_选择Q群.Location = new Point(217, 3);
            button_选择Q群.Name = "button_选择Q群";
            button_选择Q群.Size = new Size(75, 28);
            button_选择Q群.TabIndex = 7;
            button_选择Q群.Text = "选择Q群";
            button_选择Q群.UseVisualStyleBackColor = true;
            // 
            // comboBox_WorkGroupId
            // 
            comboBox_WorkGroupId.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_WorkGroupId.FormattingEnabled = true;
            comboBox_WorkGroupId.Location = new Point(4, 5);
            comboBox_WorkGroupId.Name = "comboBox_WorkGroupId";
            comboBox_WorkGroupId.Size = new Size(207, 25);
            comboBox_WorkGroupId.TabIndex = 2;
            // 
            // panel9
            // 
            panel9.Controls.Add(label_总积分);
            panel9.Controls.Add(label_总人数);
            panel9.Controls.Add(button_撤销用户投注);
            panel9.Controls.Add(button_开奖历史);
            panel9.Controls.Add(label_番摊结果);
            panel9.Controls.Add(label10);
            panel9.Controls.Add(label_开奖号码);
            panel9.Controls.Add(label1);
            panel9.Controls.Add(label_收单状态);
            panel9.Controls.Add(label_开奖期数);
            panel9.Controls.Add(label_开奖期数标题);
            panel9.Controls.Add(label_封盘倒计时);
            panel9.Controls.Add(label_正在投注期数);
            panel9.Controls.Add(label_正在投注期数标题);
            panel9.Dock = DockStyle.Fill;
            panel9.Location = new Point(0, 35);
            panel9.Margin = new Padding(0);
            panel9.Name = "panel9";
            panel9.Size = new Size(773, 100);
            panel9.TabIndex = 1;
            // 
            // label_总积分
            // 
            label_总积分.BackColor = Color.LightGray;
            label_总积分.Font = new Font("宋体", 10.5F, FontStyle.Bold);
            label_总积分.ForeColor = Color.Blue;
            label_总积分.Location = new Point(462, 65);
            label_总积分.Name = "label_总积分";
            label_总积分.Size = new Size(140, 25);
            label_总积分.TabIndex = 69;
            label_总积分.Text = "总积分:";
            label_总积分.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // label_总人数
            // 
            label_总人数.BackColor = Color.LightGray;
            label_总人数.Font = new Font("宋体", 10.5F, FontStyle.Bold);
            label_总人数.ForeColor = Color.Black;
            label_总人数.Location = new Point(462, 39);
            label_总人数.Name = "label_总人数";
            label_总人数.Size = new Size(140, 25);
            label_总人数.TabIndex = 68;
            label_总人数.Text = "总人数:";
            label_总人数.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // button_撤销用户投注
            // 
            button_撤销用户投注.Location = new Point(535, 10);
            button_撤销用户投注.Name = "button_撤销用户投注";
            button_撤销用户投注.Size = new Size(90, 28);
            button_撤销用户投注.TabIndex = 67;
            button_撤销用户投注.Text = "撤销用户投注";
            button_撤销用户投注.UseVisualStyleBackColor = true;
            // 
            // button_开奖历史
            // 
            button_开奖历史.Location = new Point(462, 10);
            button_开奖历史.Name = "button_开奖历史";
            button_开奖历史.Size = new Size(69, 28);
            button_开奖历史.TabIndex = 66;
            button_开奖历史.Text = "开奖历史";
            button_开奖历史.UseVisualStyleBackColor = true;
            // 
            // label_番摊结果
            // 
            label_番摊结果.BackColor = Color.White;
            label_番摊结果.BorderStyle = BorderStyle.FixedSingle;
            label_番摊结果.Font = new Font("宋体", 18F, FontStyle.Bold);
            label_番摊结果.ForeColor = Color.Blue;
            label_番摊结果.Location = new Point(364, 38);
            label_番摊结果.Name = "label_番摊结果";
            label_番摊结果.Size = new Size(85, 50);
            label_番摊结果.TabIndex = 65;
            label_番摊结果.Text = "-";
            label_番摊结果.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label10
            // 
            label10.BackColor = Color.FromArgb(0, 0, 192);
            label10.Font = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label10.ForeColor = Color.White;
            label10.Location = new Point(364, 13);
            label10.Name = "label10";
            label10.Size = new Size(85, 25);
            label10.TabIndex = 64;
            label10.Text = "番摊";
            label10.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label_开奖号码
            // 
            label_开奖号码.BackColor = Color.White;
            label_开奖号码.BorderStyle = BorderStyle.FixedSingle;
            label_开奖号码.Font = new Font("宋体", 18F, FontStyle.Bold);
            label_开奖号码.ForeColor = Color.Blue;
            label_开奖号码.Location = new Point(273, 38);
            label_开奖号码.Name = "label_开奖号码";
            label_开奖号码.Size = new Size(85, 50);
            label_开奖号码.TabIndex = 63;
            label_开奖号码.Text = "--";
            label_开奖号码.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label1
            // 
            label1.BackColor = Color.FromArgb(0, 0, 192);
            label1.Font = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label1.ForeColor = Color.White;
            label1.Location = new Point(273, 13);
            label1.Name = "label1";
            label1.Size = new Size(85, 25);
            label1.TabIndex = 62;
            label1.Text = "号码";
            label1.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label_收单状态
            // 
            label_收单状态.BackColor = Color.Cyan;
            label_收单状态.BorderStyle = BorderStyle.FixedSingle;
            label_收单状态.Font = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label_收单状态.ForeColor = Color.Red;
            label_收单状态.Location = new Point(145, 62);
            label_收单状态.Name = "label_收单状态";
            label_收单状态.Size = new Size(121, 25);
            label_收单状态.TabIndex = 61;
            label_收单状态.Text = "停止收单";
            label_收单状态.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label_开奖期数
            // 
            label_开奖期数.BackColor = Color.White;
            label_开奖期数.BorderStyle = BorderStyle.FixedSingle;
            label_开奖期数.Font = new Font("宋体", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label_开奖期数.ForeColor = Color.Red;
            label_开奖期数.Location = new Point(145, 38);
            label_开奖期数.Name = "label_开奖期数";
            label_开奖期数.Size = new Size(121, 25);
            label_开奖期数.TabIndex = 60;
            label_开奖期数.Text = "0";
            label_开奖期数.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label_开奖期数标题
            // 
            label_开奖期数标题.BackColor = Color.Red;
            label_开奖期数标题.Font = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label_开奖期数标题.ForeColor = Color.White;
            label_开奖期数标题.Location = new Point(145, 13);
            label_开奖期数标题.Name = "label_开奖期数标题";
            label_开奖期数标题.Size = new Size(121, 25);
            label_开奖期数标题.TabIndex = 59;
            label_开奖期数标题.Text = "开奖期数";
            label_开奖期数标题.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label_封盘倒计时
            // 
            label_封盘倒计时.BackColor = Color.LightGray;
            label_封盘倒计时.BorderStyle = BorderStyle.FixedSingle;
            label_封盘倒计时.Font = new Font("宋体", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label_封盘倒计时.ForeColor = Color.Blue;
            label_封盘倒计时.Location = new Point(6, 62);
            label_封盘倒计时.Name = "label_封盘倒计时";
            label_封盘倒计时.Size = new Size(131, 25);
            label_封盘倒计时.TabIndex = 58;
            label_封盘倒计时.Text = "00:00:00";
            label_封盘倒计时.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label_正在投注期数
            // 
            label_正在投注期数.BackColor = Color.White;
            label_正在投注期数.BorderStyle = BorderStyle.FixedSingle;
            label_正在投注期数.Font = new Font("宋体", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label_正在投注期数.ForeColor = Color.Red;
            label_正在投注期数.Location = new Point(6, 38);
            label_正在投注期数.Name = "label_正在投注期数";
            label_正在投注期数.Size = new Size(131, 25);
            label_正在投注期数.TabIndex = 57;
            label_正在投注期数.Text = "0";
            label_正在投注期数.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label_正在投注期数标题
            // 
            label_正在投注期数标题.BackColor = Color.Red;
            label_正在投注期数标题.Font = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label_正在投注期数标题.ForeColor = Color.White;
            label_正在投注期数标题.Location = new Point(6, 13);
            label_正在投注期数标题.Name = "label_正在投注期数标题";
            label_正在投注期数标题.Size = new Size(131, 25);
            label_正在投注期数标题.TabIndex = 56;
            label_正在投注期数标题.Text = "正在投注期数";
            label_正在投注期数标题.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // panel10
            // 
            panel10.Controls.Add(button_StopBet);
            panel10.Controls.Add(button_StartBet);
            panel10.Controls.Add(button_StopService);
            panel10.Controls.Add(button_StartService);
            panel10.Dock = DockStyle.Fill;
            panel10.Location = new Point(0, 641);
            panel10.Margin = new Padding(0);
            panel10.Name = "panel10";
            panel10.Size = new Size(773, 35);
            panel10.TabIndex = 3;
            // 
            // button_StopBet
            // 
            button_StopBet.Enabled = false;
            button_StopBet.Location = new Point(531, 3);
            button_StopBet.Name = "button_StopBet";
            button_StopBet.Size = new Size(104, 30);
            button_StopBet.TabIndex = 70;
            button_StopBet.Text = "停止飞单";
            button_StopBet.UseVisualStyleBackColor = true;
            button_StopBet.Click += button_StopBet_Click;
            // 
            // button_StartBet
            // 
            button_StartBet.Enabled = false;
            button_StartBet.Location = new Point(427, 3);
            button_StartBet.Name = "button_StartBet";
            button_StartBet.Size = new Size(104, 30);
            button_StartBet.TabIndex = 69;
            button_StartBet.Text = "开始飞单";
            button_StartBet.UseVisualStyleBackColor = true;
            button_StartBet.Click += button_StartBet_Click;
            // 
            // button_StopService
            // 
            button_StopService.Enabled = false;
            button_StopService.Location = new Point(323, 3);
            button_StopService.Name = "button_StopService";
            button_StopService.Size = new Size(104, 30);
            button_StopService.TabIndex = 68;
            button_StopService.Text = "停止游戏";
            button_StopService.UseVisualStyleBackColor = true;
            button_StopService.Click += button_StopService_Click;
            // 
            // button_StartService
            // 
            button_StartService.Location = new Point(219, 3);
            button_StartService.Name = "button_StartService";
            button_StartService.Size = new Size(104, 30);
            button_StartService.TabIndex = 67;
            button_StartService.Text = "开始游戏";
            button_StartService.UseVisualStyleBackColor = true;
            button_StartService.Click += button_StartService_Click;
            // 
            // tabPage_BetOrderReport
            // 
            tabPage_BetOrderReport.Controls.Add(tableLayoutPanel2);
            tabPage_BetOrderReport.Location = new Point(4, 26);
            tabPage_BetOrderReport.Name = "tabPage_BetOrderReport";
            tabPage_BetOrderReport.Padding = new Padding(3);
            tabPage_BetOrderReport.Size = new Size(1275, 678);
            tabPage_BetOrderReport.TabIndex = 1;
            tabPage_BetOrderReport.Text = "▶投注明细";
            tabPage_BetOrderReport.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel2
            // 
            tableLayoutPanel2.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel2.ColumnCount = 1;
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel2.Controls.Add(dataGridView_BetOrderReport, 0, 1);
            tableLayoutPanel2.Controls.Add(panel1, 0, 0);
            tableLayoutPanel2.Dock = DockStyle.Fill;
            tableLayoutPanel2.Location = new Point(3, 3);
            tableLayoutPanel2.Margin = new Padding(0);
            tableLayoutPanel2.Name = "tableLayoutPanel2";
            tableLayoutPanel2.RowCount = 2;
            tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel2.Size = new Size(1269, 672);
            tableLayoutPanel2.TabIndex = 2;
            // 
            // dataGridView_BetOrderReport
            // 
            dataGridViewCellStyle13.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle13.BackColor = SystemColors.Control;
            dataGridViewCellStyle13.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle13.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle13.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle13.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle13.WrapMode = DataGridViewTriState.True;
            dataGridView_BetOrderReport.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle13;
            dataGridView_BetOrderReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle14.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle14.BackColor = SystemColors.Window;
            dataGridViewCellStyle14.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle14.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle14.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle14.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle14.WrapMode = DataGridViewTriState.False;
            dataGridView_BetOrderReport.DefaultCellStyle = dataGridViewCellStyle14;
            dataGridView_BetOrderReport.Dock = DockStyle.Fill;
            dataGridView_BetOrderReport.Location = new Point(4, 40);
            dataGridView_BetOrderReport.Name = "dataGridView_BetOrderReport";
            dataGridView_BetOrderReport.ReadOnly = true;
            dataGridViewCellStyle15.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle15.BackColor = SystemColors.Control;
            dataGridViewCellStyle15.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle15.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle15.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle15.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle15.WrapMode = DataGridViewTriState.True;
            dataGridView_BetOrderReport.RowHeadersDefaultCellStyle = dataGridViewCellStyle15;
            dataGridView_BetOrderReport.Size = new Size(1261, 628);
            dataGridView_BetOrderReport.TabIndex = 1;
            dataGridView_BetOrderReport.VirtualMode = true;
            // 
            // panel1
            // 
            panel1.Controls.Add(comboBox_SelectMemberForBetOrder);
            panel1.Controls.Add(label4);
            panel1.Controls.Add(comboBox_SelectIssueForBetOrder);
            panel1.Controls.Add(label3);
            panel1.Dock = DockStyle.Fill;
            panel1.Location = new Point(4, 4);
            panel1.Name = "panel1";
            panel1.Size = new Size(1261, 29);
            panel1.TabIndex = 0;
            // 
            // comboBox_SelectMemberForBetOrder
            // 
            comboBox_SelectMemberForBetOrder.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectMemberForBetOrder.FormattingEnabled = true;
            comboBox_SelectMemberForBetOrder.Location = new Point(89, 3);
            comboBox_SelectMemberForBetOrder.Name = "comboBox_SelectMemberForBetOrder";
            comboBox_SelectMemberForBetOrder.Size = new Size(250, 25);
            comboBox_SelectMemberForBetOrder.TabIndex = 3;
            comboBox_SelectMemberForBetOrder.SelectedIndexChanged += comboBox_SelectMemberForBetOrder_SelectedIndexChanged;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label4.ForeColor = Color.Red;
            label4.Location = new Point(4, 6);
            label4.Name = "label4";
            label4.Size = new Size(83, 19);
            label4.TabIndex = 2;
            label4.Text = "按会员查询:";
            // 
            // comboBox_SelectIssueForBetOrder
            // 
            comboBox_SelectIssueForBetOrder.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectIssueForBetOrder.FormattingEnabled = true;
            comboBox_SelectIssueForBetOrder.Location = new Point(431, 3);
            comboBox_SelectIssueForBetOrder.Name = "comboBox_SelectIssueForBetOrder";
            comboBox_SelectIssueForBetOrder.Size = new Size(170, 25);
            comboBox_SelectIssueForBetOrder.TabIndex = 1;
            comboBox_SelectIssueForBetOrder.SelectedIndexChanged += comboBox_SelectIssueForBetOrder_SelectedIndexChanged;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label3.ForeColor = Color.Red;
            label3.Location = new Point(345, 6);
            label3.Name = "label3";
            label3.Size = new Size(83, 19);
            label3.TabIndex = 0;
            label3.Text = "按期号查询:";
            // 
            // tabPage_BetTotalReport
            // 
            tabPage_BetTotalReport.Controls.Add(tableLayoutPanel3);
            tabPage_BetTotalReport.Location = new Point(4, 26);
            tabPage_BetTotalReport.Name = "tabPage_BetTotalReport";
            tabPage_BetTotalReport.Padding = new Padding(3);
            tabPage_BetTotalReport.Size = new Size(1275, 678);
            tabPage_BetTotalReport.TabIndex = 2;
            tabPage_BetTotalReport.Text = "▶飞单汇总";
            tabPage_BetTotalReport.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel3
            // 
            tableLayoutPanel3.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel3.ColumnCount = 1;
            tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel3.Controls.Add(dataGridView_HuiZongReport, 0, 1);
            tableLayoutPanel3.Controls.Add(panel2, 0, 0);
            tableLayoutPanel3.Dock = DockStyle.Fill;
            tableLayoutPanel3.Location = new Point(3, 3);
            tableLayoutPanel3.Margin = new Padding(0);
            tableLayoutPanel3.Name = "tableLayoutPanel3";
            tableLayoutPanel3.RowCount = 2;
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel3.Size = new Size(1269, 672);
            tableLayoutPanel3.TabIndex = 3;
            // 
            // dataGridView_HuiZongReport
            // 
            dataGridViewCellStyle16.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle16.BackColor = SystemColors.Control;
            dataGridViewCellStyle16.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle16.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle16.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle16.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle16.WrapMode = DataGridViewTriState.True;
            dataGridView_HuiZongReport.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle16;
            dataGridView_HuiZongReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle17.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle17.BackColor = SystemColors.Window;
            dataGridViewCellStyle17.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle17.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle17.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle17.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle17.WrapMode = DataGridViewTriState.False;
            dataGridView_HuiZongReport.DefaultCellStyle = dataGridViewCellStyle17;
            dataGridView_HuiZongReport.Dock = DockStyle.Fill;
            dataGridView_HuiZongReport.Location = new Point(4, 40);
            dataGridView_HuiZongReport.Name = "dataGridView_HuiZongReport";
            dataGridView_HuiZongReport.ReadOnly = true;
            dataGridViewCellStyle18.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle18.BackColor = SystemColors.Control;
            dataGridViewCellStyle18.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle18.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle18.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle18.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle18.WrapMode = DataGridViewTriState.True;
            dataGridView_HuiZongReport.RowHeadersDefaultCellStyle = dataGridViewCellStyle18;
            dataGridView_HuiZongReport.Size = new Size(1261, 628);
            dataGridView_HuiZongReport.TabIndex = 2;
            // 
            // panel2
            // 
            panel2.Controls.Add(comboBox_SelectIssueForHuiZong);
            panel2.Controls.Add(label6);
            panel2.Dock = DockStyle.Fill;
            panel2.Location = new Point(4, 4);
            panel2.Name = "panel2";
            panel2.Size = new Size(1261, 29);
            panel2.TabIndex = 1;
            // 
            // comboBox_SelectIssueForHuiZong
            // 
            comboBox_SelectIssueForHuiZong.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectIssueForHuiZong.FormattingEnabled = true;
            comboBox_SelectIssueForHuiZong.Location = new Point(77, 2);
            comboBox_SelectIssueForHuiZong.Name = "comboBox_SelectIssueForHuiZong";
            comboBox_SelectIssueForHuiZong.Size = new Size(250, 25);
            comboBox_SelectIssueForHuiZong.TabIndex = 1;
            comboBox_SelectIssueForHuiZong.SelectedIndexChanged += comboBox_SelectIssueForHuiZong_SelectedIndexChanged;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label6.ForeColor = Color.Red;
            label6.Location = new Point(2, 5);
            label6.Name = "label6";
            label6.Size = new Size(69, 19);
            label6.TabIndex = 0;
            label6.Text = "查询期号:";
            // 
            // tabPage_AddMoneyReport
            // 
            tabPage_AddMoneyReport.Controls.Add(tableLayoutPanel4);
            tabPage_AddMoneyReport.Location = new Point(4, 26);
            tabPage_AddMoneyReport.Name = "tabPage_AddMoneyReport";
            tabPage_AddMoneyReport.Size = new Size(1275, 678);
            tabPage_AddMoneyReport.TabIndex = 3;
            tabPage_AddMoneyReport.Text = "▶上分记录";
            tabPage_AddMoneyReport.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel4
            // 
            tableLayoutPanel4.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel4.ColumnCount = 1;
            tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel4.Controls.Add(dataGridView_AddMoneyReport, 0, 1);
            tableLayoutPanel4.Controls.Add(panel3, 0, 0);
            tableLayoutPanel4.Dock = DockStyle.Fill;
            tableLayoutPanel4.Location = new Point(0, 0);
            tableLayoutPanel4.Margin = new Padding(0);
            tableLayoutPanel4.Name = "tableLayoutPanel4";
            tableLayoutPanel4.RowCount = 2;
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel4.Size = new Size(1275, 678);
            tableLayoutPanel4.TabIndex = 3;
            // 
            // dataGridView_AddMoneyReport
            // 
            dataGridViewCellStyle19.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle19.BackColor = SystemColors.Control;
            dataGridViewCellStyle19.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle19.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle19.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle19.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle19.WrapMode = DataGridViewTriState.True;
            dataGridView_AddMoneyReport.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle19;
            dataGridView_AddMoneyReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle20.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle20.BackColor = SystemColors.Window;
            dataGridViewCellStyle20.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle20.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle20.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle20.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle20.WrapMode = DataGridViewTriState.False;
            dataGridView_AddMoneyReport.DefaultCellStyle = dataGridViewCellStyle20;
            dataGridView_AddMoneyReport.Dock = DockStyle.Fill;
            dataGridView_AddMoneyReport.Location = new Point(4, 40);
            dataGridView_AddMoneyReport.Name = "dataGridView_AddMoneyReport";
            dataGridView_AddMoneyReport.ReadOnly = true;
            dataGridViewCellStyle21.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle21.BackColor = SystemColors.Control;
            dataGridViewCellStyle21.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle21.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle21.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle21.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle21.WrapMode = DataGridViewTriState.True;
            dataGridView_AddMoneyReport.RowHeadersDefaultCellStyle = dataGridViewCellStyle21;
            dataGridView_AddMoneyReport.Size = new Size(1267, 634);
            dataGridView_AddMoneyReport.TabIndex = 2;
            // 
            // panel3
            // 
            panel3.Controls.Add(comboBox_SelectObjForAddMoneyReport);
            panel3.Controls.Add(label7);
            panel3.Dock = DockStyle.Fill;
            panel3.Location = new Point(4, 4);
            panel3.Name = "panel3";
            panel3.Size = new Size(1267, 29);
            panel3.TabIndex = 1;
            // 
            // comboBox_SelectObjForAddMoneyReport
            // 
            comboBox_SelectObjForAddMoneyReport.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectObjForAddMoneyReport.FormattingEnabled = true;
            comboBox_SelectObjForAddMoneyReport.Location = new Point(77, 2);
            comboBox_SelectObjForAddMoneyReport.Name = "comboBox_SelectObjForAddMoneyReport";
            comboBox_SelectObjForAddMoneyReport.Size = new Size(250, 25);
            comboBox_SelectObjForAddMoneyReport.TabIndex = 1;
            comboBox_SelectObjForAddMoneyReport.SelectedIndexChanged += comboBox_SelectObjForAddMoneyReport_SelectedIndexChanged;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label7.ForeColor = Color.Red;
            label7.Location = new Point(2, 5);
            label7.Name = "label7";
            label7.Size = new Size(69, 19);
            label7.TabIndex = 0;
            label7.Text = "查询会员:";
            // 
            // tabPage_SubMoneyReport
            // 
            tabPage_SubMoneyReport.Controls.Add(tableLayoutPanel5);
            tabPage_SubMoneyReport.Location = new Point(4, 26);
            tabPage_SubMoneyReport.Name = "tabPage_SubMoneyReport";
            tabPage_SubMoneyReport.Size = new Size(1275, 678);
            tabPage_SubMoneyReport.TabIndex = 4;
            tabPage_SubMoneyReport.Text = "▶下分记录";
            tabPage_SubMoneyReport.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel5
            // 
            tableLayoutPanel5.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel5.ColumnCount = 1;
            tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel5.Controls.Add(dataGridView_SubMoneyReport, 0, 1);
            tableLayoutPanel5.Controls.Add(panel4, 0, 0);
            tableLayoutPanel5.Dock = DockStyle.Fill;
            tableLayoutPanel5.Location = new Point(0, 0);
            tableLayoutPanel5.Margin = new Padding(0);
            tableLayoutPanel5.Name = "tableLayoutPanel5";
            tableLayoutPanel5.RowCount = 2;
            tableLayoutPanel5.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel5.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel5.Size = new Size(1275, 678);
            tableLayoutPanel5.TabIndex = 3;
            // 
            // dataGridView_SubMoneyReport
            // 
            dataGridViewCellStyle22.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle22.BackColor = SystemColors.Control;
            dataGridViewCellStyle22.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle22.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle22.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle22.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle22.WrapMode = DataGridViewTriState.True;
            dataGridView_SubMoneyReport.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle22;
            dataGridView_SubMoneyReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle23.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle23.BackColor = SystemColors.Window;
            dataGridViewCellStyle23.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle23.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle23.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle23.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle23.WrapMode = DataGridViewTriState.False;
            dataGridView_SubMoneyReport.DefaultCellStyle = dataGridViewCellStyle23;
            dataGridView_SubMoneyReport.Dock = DockStyle.Fill;
            dataGridView_SubMoneyReport.Location = new Point(4, 40);
            dataGridView_SubMoneyReport.Name = "dataGridView_SubMoneyReport";
            dataGridView_SubMoneyReport.ReadOnly = true;
            dataGridViewCellStyle24.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle24.BackColor = SystemColors.Control;
            dataGridViewCellStyle24.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle24.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle24.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle24.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle24.WrapMode = DataGridViewTriState.True;
            dataGridView_SubMoneyReport.RowHeadersDefaultCellStyle = dataGridViewCellStyle24;
            dataGridView_SubMoneyReport.Size = new Size(1267, 634);
            dataGridView_SubMoneyReport.TabIndex = 2;
            // 
            // panel4
            // 
            panel4.Controls.Add(comboBox_SelectObjForSubMoneyReport);
            panel4.Controls.Add(label8);
            panel4.Dock = DockStyle.Fill;
            panel4.Location = new Point(4, 4);
            panel4.Name = "panel4";
            panel4.Size = new Size(1267, 29);
            panel4.TabIndex = 1;
            // 
            // comboBox_SelectObjForSubMoneyReport
            // 
            comboBox_SelectObjForSubMoneyReport.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectObjForSubMoneyReport.FormattingEnabled = true;
            comboBox_SelectObjForSubMoneyReport.Location = new Point(77, 2);
            comboBox_SelectObjForSubMoneyReport.Name = "comboBox_SelectObjForSubMoneyReport";
            comboBox_SelectObjForSubMoneyReport.Size = new Size(250, 25);
            comboBox_SelectObjForSubMoneyReport.TabIndex = 1;
            comboBox_SelectObjForSubMoneyReport.SelectedIndexChanged += comboBox_SelectObjForSubMoneyReport_SelectedIndexChanged;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label8.ForeColor = Color.Red;
            label8.Location = new Point(2, 5);
            label8.Name = "label8";
            label8.Size = new Size(69, 19);
            label8.TabIndex = 0;
            label8.Text = "查询会员:";
            // 
            // tabPage_FinanceReport
            // 
            tabPage_FinanceReport.Controls.Add(tableLayoutPanel6);
            tabPage_FinanceReport.Location = new Point(4, 26);
            tabPage_FinanceReport.Name = "tabPage_FinanceReport";
            tabPage_FinanceReport.Size = new Size(1275, 678);
            tabPage_FinanceReport.TabIndex = 5;
            tabPage_FinanceReport.Text = "▶财务记录";
            tabPage_FinanceReport.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel6
            // 
            tableLayoutPanel6.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel6.ColumnCount = 1;
            tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel6.Controls.Add(dataGridView_FinanceReport, 0, 1);
            tableLayoutPanel6.Controls.Add(panel5, 0, 0);
            tableLayoutPanel6.Dock = DockStyle.Fill;
            tableLayoutPanel6.Location = new Point(0, 0);
            tableLayoutPanel6.Margin = new Padding(0);
            tableLayoutPanel6.Name = "tableLayoutPanel6";
            tableLayoutPanel6.RowCount = 2;
            tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel6.Size = new Size(1275, 678);
            tableLayoutPanel6.TabIndex = 3;
            // 
            // dataGridView_FinanceReport
            // 
            dataGridViewCellStyle25.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle25.BackColor = SystemColors.Control;
            dataGridViewCellStyle25.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle25.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle25.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle25.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle25.WrapMode = DataGridViewTriState.True;
            dataGridView_FinanceReport.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle25;
            dataGridView_FinanceReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle26.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle26.BackColor = SystemColors.Window;
            dataGridViewCellStyle26.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle26.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle26.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle26.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle26.WrapMode = DataGridViewTriState.False;
            dataGridView_FinanceReport.DefaultCellStyle = dataGridViewCellStyle26;
            dataGridView_FinanceReport.Dock = DockStyle.Fill;
            dataGridView_FinanceReport.Location = new Point(4, 40);
            dataGridView_FinanceReport.Name = "dataGridView_FinanceReport";
            dataGridView_FinanceReport.ReadOnly = true;
            dataGridViewCellStyle27.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle27.BackColor = SystemColors.Control;
            dataGridViewCellStyle27.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle27.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle27.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle27.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle27.WrapMode = DataGridViewTriState.True;
            dataGridView_FinanceReport.RowHeadersDefaultCellStyle = dataGridViewCellStyle27;
            dataGridView_FinanceReport.Size = new Size(1267, 634);
            dataGridView_FinanceReport.TabIndex = 3;
            dataGridView_FinanceReport.VirtualMode = true;
            // 
            // panel5
            // 
            panel5.Controls.Add(comboBox_SelectObjForFinanceReport);
            panel5.Controls.Add(label5);
            panel5.Dock = DockStyle.Fill;
            panel5.Location = new Point(4, 4);
            panel5.Name = "panel5";
            panel5.Size = new Size(1267, 29);
            panel5.TabIndex = 2;
            // 
            // comboBox_SelectObjForFinanceReport
            // 
            comboBox_SelectObjForFinanceReport.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectObjForFinanceReport.FormattingEnabled = true;
            comboBox_SelectObjForFinanceReport.Location = new Point(77, 2);
            comboBox_SelectObjForFinanceReport.Name = "comboBox_SelectObjForFinanceReport";
            comboBox_SelectObjForFinanceReport.Size = new Size(250, 25);
            comboBox_SelectObjForFinanceReport.TabIndex = 1;
            comboBox_SelectObjForFinanceReport.SelectedIndexChanged += comboBox_SelectObjForFinanceReport_SelectedIndexChanged;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label5.ForeColor = Color.Red;
            label5.Location = new Point(2, 5);
            label5.Name = "label5";
            label5.Size = new Size(69, 19);
            label5.TabIndex = 0;
            label5.Text = "查询会员:";
            // 
            // tabPage_LaShou
            // 
            tabPage_LaShou.Controls.Add(tableLayoutPanel9);
            tabPage_LaShou.Location = new Point(4, 26);
            tabPage_LaShou.Name = "tabPage_LaShou";
            tabPage_LaShou.Padding = new Padding(3);
            tabPage_LaShou.Size = new Size(1275, 678);
            tabPage_LaShou.TabIndex = 11;
            tabPage_LaShou.Text = "▶拉手明细";
            tabPage_LaShou.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel9
            // 
            tableLayoutPanel9.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel9.ColumnCount = 1;
            tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel9.Controls.Add(dataGridView_LaShou, 0, 1);
            tableLayoutPanel9.Controls.Add(panel7, 0, 0);
            tableLayoutPanel9.Dock = DockStyle.Fill;
            tableLayoutPanel9.Location = new Point(3, 3);
            tableLayoutPanel9.Margin = new Padding(0);
            tableLayoutPanel9.Name = "tableLayoutPanel9";
            tableLayoutPanel9.RowCount = 2;
            tableLayoutPanel9.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel9.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel9.Size = new Size(1269, 672);
            tableLayoutPanel9.TabIndex = 4;
            // 
            // dataGridView_LaShou
            // 
            dataGridViewCellStyle28.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle28.BackColor = SystemColors.Control;
            dataGridViewCellStyle28.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle28.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle28.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle28.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle28.WrapMode = DataGridViewTriState.True;
            dataGridView_LaShou.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle28;
            dataGridView_LaShou.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle29.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle29.BackColor = SystemColors.Window;
            dataGridViewCellStyle29.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle29.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle29.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle29.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle29.WrapMode = DataGridViewTriState.False;
            dataGridView_LaShou.DefaultCellStyle = dataGridViewCellStyle29;
            dataGridView_LaShou.Dock = DockStyle.Fill;
            dataGridView_LaShou.Location = new Point(4, 40);
            dataGridView_LaShou.Name = "dataGridView_LaShou";
            dataGridView_LaShou.ReadOnly = true;
            dataGridViewCellStyle30.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle30.BackColor = SystemColors.Control;
            dataGridViewCellStyle30.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle30.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle30.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle30.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle30.WrapMode = DataGridViewTriState.True;
            dataGridView_LaShou.RowHeadersDefaultCellStyle = dataGridViewCellStyle30;
            dataGridView_LaShou.Size = new Size(1261, 628);
            dataGridView_LaShou.TabIndex = 3;
            dataGridView_LaShou.VirtualMode = true;
            // 
            // panel7
            // 
            panel7.Controls.Add(label_LaShouTips);
            panel7.Controls.Add(uiButton_JieSuanLaShou);
            panel7.Controls.Add(uiButton_CheckLaShou);
            panel7.Dock = DockStyle.Fill;
            panel7.Location = new Point(4, 4);
            panel7.Name = "panel7";
            panel7.Size = new Size(1261, 29);
            panel7.TabIndex = 2;
            // 
            // label_LaShouTips
            // 
            label_LaShouTips.AutoSize = true;
            label_LaShouTips.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label_LaShouTips.ForeColor = Color.Red;
            label_LaShouTips.Location = new Point(135, 5);
            label_LaShouTips.Name = "label_LaShouTips";
            label_LaShouTips.Size = new Size(403, 19);
            label_LaShouTips.TabIndex = 19;
            label_LaShouTips.Text = "注意: 预查询可随时点击查看, 执行结算每天手工后执行一次即可";
            // 
            // uiButton_JieSuanLaShou
            // 
            uiButton_JieSuanLaShou.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            uiButton_JieSuanLaShou.Cursor = Cursors.Hand;
            uiButton_JieSuanLaShou.FillColor = Color.FromArgb(230, 80, 80);
            uiButton_JieSuanLaShou.FillColor2 = Color.FromArgb(230, 80, 80);
            uiButton_JieSuanLaShou.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_JieSuanLaShou.Location = new Point(1126, 2);
            uiButton_JieSuanLaShou.MinimumSize = new Size(1, 1);
            uiButton_JieSuanLaShou.Name = "uiButton_JieSuanLaShou";
            uiButton_JieSuanLaShou.RectColor = Color.FromArgb(230, 80, 80);
            uiButton_JieSuanLaShou.Size = new Size(132, 25);
            uiButton_JieSuanLaShou.TabIndex = 18;
            uiButton_JieSuanLaShou.Text = "结算拉手返利";
            uiButton_JieSuanLaShou.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_JieSuanLaShou.Click += uiButton_JieSuanLaShou_Click;
            // 
            // uiButton_CheckLaShou
            // 
            uiButton_CheckLaShou.Cursor = Cursors.Hand;
            uiButton_CheckLaShou.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_CheckLaShou.Location = new Point(3, 2);
            uiButton_CheckLaShou.MinimumSize = new Size(1, 1);
            uiButton_CheckLaShou.Name = "uiButton_CheckLaShou";
            uiButton_CheckLaShou.Size = new Size(122, 25);
            uiButton_CheckLaShou.TabIndex = 17;
            uiButton_CheckLaShou.Text = "预查询";
            uiButton_CheckLaShou.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_CheckLaShou.Click += uiButton_CheckLaShou_Click;
            // 
            // tabPage_RecMsg
            // 
            tabPage_RecMsg.Controls.Add(tableLayoutPanel7);
            tabPage_RecMsg.Location = new Point(4, 26);
            tabPage_RecMsg.Name = "tabPage_RecMsg";
            tabPage_RecMsg.Size = new Size(1275, 678);
            tabPage_RecMsg.TabIndex = 6;
            tabPage_RecMsg.Text = "▶信息记录";
            tabPage_RecMsg.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel7
            // 
            tableLayoutPanel7.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel7.ColumnCount = 1;
            tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel7.Controls.Add(dataGridView_RecMsg, 0, 1);
            tableLayoutPanel7.Controls.Add(panel6, 0, 0);
            tableLayoutPanel7.Dock = DockStyle.Fill;
            tableLayoutPanel7.Location = new Point(0, 0);
            tableLayoutPanel7.Margin = new Padding(0);
            tableLayoutPanel7.Name = "tableLayoutPanel7";
            tableLayoutPanel7.RowCount = 2;
            tableLayoutPanel7.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel7.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel7.Size = new Size(1275, 678);
            tableLayoutPanel7.TabIndex = 3;
            // 
            // dataGridView_RecMsg
            // 
            dataGridViewCellStyle31.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle31.BackColor = SystemColors.Control;
            dataGridViewCellStyle31.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle31.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle31.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle31.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle31.WrapMode = DataGridViewTriState.True;
            dataGridView_RecMsg.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle31;
            dataGridView_RecMsg.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle32.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle32.BackColor = SystemColors.Window;
            dataGridViewCellStyle32.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle32.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle32.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle32.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle32.WrapMode = DataGridViewTriState.False;
            dataGridView_RecMsg.DefaultCellStyle = dataGridViewCellStyle32;
            dataGridView_RecMsg.Dock = DockStyle.Fill;
            dataGridView_RecMsg.Location = new Point(4, 40);
            dataGridView_RecMsg.Name = "dataGridView_RecMsg";
            dataGridView_RecMsg.ReadOnly = true;
            dataGridViewCellStyle33.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle33.BackColor = SystemColors.Control;
            dataGridViewCellStyle33.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle33.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle33.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle33.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle33.WrapMode = DataGridViewTriState.True;
            dataGridView_RecMsg.RowHeadersDefaultCellStyle = dataGridViewCellStyle33;
            dataGridView_RecMsg.Size = new Size(1267, 634);
            dataGridView_RecMsg.TabIndex = 3;
            dataGridView_RecMsg.VirtualMode = true;
            // 
            // panel6
            // 
            panel6.Controls.Add(comboBox_SelectMemberForShowRecMsg);
            panel6.Controls.Add(label9);
            panel6.Dock = DockStyle.Fill;
            panel6.Location = new Point(4, 4);
            panel6.Name = "panel6";
            panel6.Size = new Size(1267, 29);
            panel6.TabIndex = 2;
            // 
            // comboBox_SelectMemberForShowRecMsg
            // 
            comboBox_SelectMemberForShowRecMsg.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectMemberForShowRecMsg.FormattingEnabled = true;
            comboBox_SelectMemberForShowRecMsg.Location = new Point(77, 2);
            comboBox_SelectMemberForShowRecMsg.Name = "comboBox_SelectMemberForShowRecMsg";
            comboBox_SelectMemberForShowRecMsg.Size = new Size(250, 25);
            comboBox_SelectMemberForShowRecMsg.TabIndex = 1;
            comboBox_SelectMemberForShowRecMsg.SelectedIndexChanged += comboBox_SelectMemberForShowRecMsg_SelectedIndexChanged;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label9.ForeColor = Color.Red;
            label9.Location = new Point(2, 5);
            label9.Name = "label9";
            label9.Size = new Size(69, 19);
            label9.TabIndex = 0;
            label9.Text = "查询会员:";
            // 
            // tabPage_Setting
            // 
            tabPage_Setting.Controls.Add(tableLayoutPanel8);
            tabPage_Setting.Location = new Point(4, 26);
            tabPage_Setting.Name = "tabPage_Setting";
            tabPage_Setting.Size = new Size(1275, 678);
            tabPage_Setting.TabIndex = 7;
            tabPage_Setting.Text = "▶设置";
            tabPage_Setting.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel8
            // 
            tableLayoutPanel8.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel8.ColumnCount = 1;
            tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            tableLayoutPanel8.Controls.Add(panel_Setting, 0, 0);
            tableLayoutPanel8.Dock = DockStyle.Fill;
            tableLayoutPanel8.Location = new Point(0, 0);
            tableLayoutPanel8.Margin = new Padding(0);
            tableLayoutPanel8.Name = "tableLayoutPanel8";
            tableLayoutPanel8.RowCount = 1;
            tableLayoutPanel8.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel8.Size = new Size(1275, 678);
            tableLayoutPanel8.TabIndex = 4;
            // 
            // panel_Setting
            // 
            panel_Setting.Controls.Add(dataGridView_PlatformLog);
            panel_Setting.Controls.Add(uiButton_ShowTime);
            panel_Setting.Controls.Add(groupBox4);
            panel_Setting.Controls.Add(groupBox8);
            panel_Setting.Controls.Add(groupBox6);
            panel_Setting.Controls.Add(groupBox5);
            panel_Setting.Controls.Add(groupBox3);
            panel_Setting.Controls.Add(groupBox2);
            panel_Setting.Controls.Add(groupBox1);
            panel_Setting.Dock = DockStyle.Fill;
            panel_Setting.Location = new Point(4, 4);
            panel_Setting.Name = "panel_Setting";
            panel_Setting.Size = new Size(1267, 670);
            panel_Setting.TabIndex = 1;
            // 
            // dataGridView_PlatformLog
            // 
            dataGridView_PlatformLog.AllowUserToAddRows = false;
            dataGridViewCellStyle34.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle34.BackColor = SystemColors.Control;
            dataGridViewCellStyle34.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle34.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle34.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle34.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle34.WrapMode = DataGridViewTriState.True;
            dataGridView_PlatformLog.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle34;
            dataGridView_PlatformLog.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView_PlatformLog.Columns.AddRange(new DataGridViewColumn[] { PlatformLog_Id, PlatformLog_Time, PlatformLog_Title, PlatformLog_Content });
            dataGridViewCellStyle35.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle35.BackColor = SystemColors.Window;
            dataGridViewCellStyle35.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle35.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle35.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle35.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle35.WrapMode = DataGridViewTriState.False;
            dataGridView_PlatformLog.DefaultCellStyle = dataGridViewCellStyle35;
            dataGridView_PlatformLog.Location = new Point(362, 308);
            dataGridView_PlatformLog.Name = "dataGridView_PlatformLog";
            dataGridView_PlatformLog.ReadOnly = true;
            dataGridViewCellStyle36.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle36.BackColor = SystemColors.Control;
            dataGridViewCellStyle36.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle36.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle36.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle36.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle36.WrapMode = DataGridViewTriState.True;
            dataGridView_PlatformLog.RowHeadersDefaultCellStyle = dataGridViewCellStyle36;
            dataGridView_PlatformLog.Size = new Size(357, 173);
            dataGridView_PlatformLog.TabIndex = 19;
            dataGridView_PlatformLog.Visible = false;
            // 
            // PlatformLog_Id
            // 
            PlatformLog_Id.HeaderText = "Id";
            PlatformLog_Id.Name = "PlatformLog_Id";
            PlatformLog_Id.ReadOnly = true;
            // 
            // PlatformLog_Time
            // 
            PlatformLog_Time.HeaderText = "时间";
            PlatformLog_Time.Name = "PlatformLog_Time";
            PlatformLog_Time.ReadOnly = true;
            // 
            // PlatformLog_Title
            // 
            PlatformLog_Title.HeaderText = "摘要";
            PlatformLog_Title.Name = "PlatformLog_Title";
            PlatformLog_Title.ReadOnly = true;
            // 
            // PlatformLog_Content
            // 
            PlatformLog_Content.HeaderText = "详情";
            PlatformLog_Content.Name = "PlatformLog_Content";
            PlatformLog_Content.ReadOnly = true;
            // 
            // uiButton_ShowTime
            // 
            uiButton_ShowTime.Cursor = Cursors.Hand;
            uiButton_ShowTime.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_ShowTime.Location = new Point(345, 157);
            uiButton_ShowTime.MinimumSize = new Size(1, 1);
            uiButton_ShowTime.Name = "uiButton_ShowTime";
            uiButton_ShowTime.Size = new Size(314, 30);
            uiButton_ShowTime.TabIndex = 17;
            uiButton_ShowTime.Text = "查看标准网络时间";
            uiButton_ShowTime.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_ShowTime.Click += uiButton_ShowTime_Click;
            // 
            // groupBox4
            // 
            groupBox4.Controls.Add(uiButton_Odds);
            groupBox4.Location = new Point(13, 8);
            groupBox4.Name = "groupBox4";
            groupBox4.Size = new Size(314, 65);
            groupBox4.TabIndex = 13;
            groupBox4.TabStop = false;
            groupBox4.Text = "赔率管理";
            // 
            // uiButton_Odds
            // 
            uiButton_Odds.Cursor = Cursors.Hand;
            uiButton_Odds.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_Odds.Location = new Point(65, 22);
            uiButton_Odds.MinimumSize = new Size(1, 1);
            uiButton_Odds.Name = "uiButton_Odds";
            uiButton_Odds.Size = new Size(187, 30);
            uiButton_Odds.TabIndex = 16;
            uiButton_Odds.Text = "打开赔率管理窗口";
            uiButton_Odds.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_Odds.Click += uiButton_Odds_Click;
            // 
            // groupBox8
            // 
            groupBox8.Controls.Add(checkBox_AutoHuiShui);
            groupBox8.Controls.Add(checkBox_IsDuiChong);
            groupBox8.Controls.Add(checkBox_JiaRenAutoAddMoney);
            groupBox8.Location = new Point(13, 293);
            groupBox8.Name = "groupBox8";
            groupBox8.Size = new Size(314, 65);
            groupBox8.TabIndex = 12;
            groupBox8.TabStop = false;
            groupBox8.Text = "其它";
            // 
            // checkBox_AutoHuiShui
            // 
            checkBox_AutoHuiShui.AutoSize = true;
            checkBox_AutoHuiShui.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            checkBox_AutoHuiShui.Location = new Point(117, 26);
            checkBox_AutoHuiShui.Name = "checkBox_AutoHuiShui";
            checkBox_AutoHuiShui.Size = new Size(75, 21);
            checkBox_AutoHuiShui.TabIndex = 10;
            checkBox_AutoHuiShui.Text = "自动回水";
            checkBox_AutoHuiShui.UseVisualStyleBackColor = true;
            checkBox_AutoHuiShui.CheckedChanged += checkBox_AutoHuiShui_CheckedChanged;
            // 
            // checkBox_IsDuiChong
            // 
            checkBox_IsDuiChong.AutoSize = true;
            checkBox_IsDuiChong.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            checkBox_IsDuiChong.Location = new Point(31, 26);
            checkBox_IsDuiChong.Name = "checkBox_IsDuiChong";
            checkBox_IsDuiChong.Size = new Size(75, 21);
            checkBox_IsDuiChong.TabIndex = 18;
            checkBox_IsDuiChong.Text = "对冲吃单";
            checkBox_IsDuiChong.UseVisualStyleBackColor = true;
            // 
            // checkBox_JiaRenAutoAddMoney
            // 
            checkBox_JiaRenAutoAddMoney.AutoSize = true;
            checkBox_JiaRenAutoAddMoney.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            checkBox_JiaRenAutoAddMoney.Location = new Point(204, 26);
            checkBox_JiaRenAutoAddMoney.Name = "checkBox_JiaRenAutoAddMoney";
            checkBox_JiaRenAutoAddMoney.Size = new Size(99, 21);
            checkBox_JiaRenAutoAddMoney.TabIndex = 11;
            checkBox_JiaRenAutoAddMoney.Text = "假人自动上分";
            checkBox_JiaRenAutoAddMoney.UseVisualStyleBackColor = true;
            checkBox_JiaRenAutoAddMoney.CheckedChanged += checkBox_JiaRenAutoAddMoney_CheckedChanged;
            // 
            // groupBox6
            // 
            groupBox6.Controls.Add(label2);
            groupBox6.Controls.Add(comboBox_CloseTime);
            groupBox6.Location = new Point(13, 80);
            groupBox6.Name = "groupBox6";
            groupBox6.Size = new Size(314, 65);
            groupBox6.TabIndex = 8;
            groupBox6.TabStop = false;
            groupBox6.Text = "封盘时间";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label2.ForeColor = Color.Red;
            label2.Location = new Point(65, 29);
            label2.Name = "label2";
            label2.Size = new Size(41, 19);
            label2.TabIndex = 6;
            label2.Text = "封盘:";
            // 
            // comboBox_CloseTime
            // 
            comboBox_CloseTime.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_CloseTime.FormattingEnabled = true;
            comboBox_CloseTime.Location = new Point(111, 26);
            comboBox_CloseTime.Name = "comboBox_CloseTime";
            comboBox_CloseTime.Size = new Size(132, 25);
            comboBox_CloseTime.TabIndex = 7;
            // 
            // groupBox5
            // 
            groupBox5.Controls.Add(uiButton_Clear);
            groupBox5.Controls.Add(checkBox_SaveMemberBalance);
            groupBox5.Controls.Add(checkBox_SaveMemberBaseInfo);
            groupBox5.Location = new Point(344, 9);
            groupBox5.Name = "groupBox5";
            groupBox5.Size = new Size(314, 65);
            groupBox5.TabIndex = 7;
            groupBox5.TabStop = false;
            groupBox5.Text = "清除数据 (基础信息包含备注名和回水比例)";
            // 
            // uiButton_Clear
            // 
            uiButton_Clear.Cursor = Cursors.Hand;
            uiButton_Clear.FillColor = Color.FromArgb(230, 80, 80);
            uiButton_Clear.FillColor2 = Color.FromArgb(230, 80, 80);
            uiButton_Clear.FillHoverColor = Color.FromArgb(235, 115, 115);
            uiButton_Clear.FillPressColor = Color.FromArgb(184, 64, 64);
            uiButton_Clear.FillSelectedColor = Color.FromArgb(184, 64, 64);
            uiButton_Clear.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_Clear.LightColor = Color.FromArgb(253, 243, 243);
            uiButton_Clear.Location = new Point(199, 23);
            uiButton_Clear.MinimumSize = new Size(1, 1);
            uiButton_Clear.Name = "uiButton_Clear";
            uiButton_Clear.RectColor = Color.FromArgb(230, 80, 80);
            uiButton_Clear.RectHoverColor = Color.FromArgb(235, 115, 115);
            uiButton_Clear.RectPressColor = Color.FromArgb(184, 64, 64);
            uiButton_Clear.RectSelectedColor = Color.FromArgb(184, 64, 64);
            uiButton_Clear.Size = new Size(100, 30);
            uiButton_Clear.Style = Sunny.UI.UIStyle.Custom;
            uiButton_Clear.TabIndex = 19;
            uiButton_Clear.Text = "清除数据";
            uiButton_Clear.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_Clear.Click += uiButton_Clear_Click;
            // 
            // checkBox_SaveMemberBalance
            // 
            checkBox_SaveMemberBalance.AutoSize = true;
            checkBox_SaveMemberBalance.Location = new Point(115, 29);
            checkBox_SaveMemberBalance.Name = "checkBox_SaveMemberBalance";
            checkBox_SaveMemberBalance.Size = new Size(75, 21);
            checkBox_SaveMemberBalance.TabIndex = 3;
            checkBox_SaveMemberBalance.Text = "保留积分";
            checkBox_SaveMemberBalance.UseVisualStyleBackColor = true;
            // 
            // checkBox_SaveMemberBaseInfo
            // 
            checkBox_SaveMemberBaseInfo.AutoSize = true;
            checkBox_SaveMemberBaseInfo.Location = new Point(12, 29);
            checkBox_SaveMemberBaseInfo.Name = "checkBox_SaveMemberBaseInfo";
            checkBox_SaveMemberBaseInfo.Size = new Size(99, 21);
            checkBox_SaveMemberBaseInfo.TabIndex = 2;
            checkBox_SaveMemberBaseInfo.Text = "保留基础信息";
            checkBox_SaveMemberBaseInfo.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(radioButton_ImgType2);
            groupBox3.Controls.Add(radioButton_ImgType1);
            groupBox3.Controls.Add(checkBox_6Rows);
            groupBox3.Controls.Add(checkBox_7Rows);
            groupBox3.Location = new Point(13, 222);
            groupBox3.Name = "groupBox3";
            groupBox3.Size = new Size(314, 65);
            groupBox3.TabIndex = 5;
            groupBox3.TabStop = false;
            groupBox3.Text = "路子图";
            // 
            // radioButton_ImgType2
            // 
            radioButton_ImgType2.AutoSize = true;
            radioButton_ImgType2.Location = new Point(236, 27);
            radioButton_ImgType2.Name = "radioButton_ImgType2";
            radioButton_ImgType2.Size = new Size(57, 21);
            radioButton_ImgType2.TabIndex = 3;
            radioButton_ImgType2.TabStop = true;
            radioButton_ImgType2.Text = "样式2";
            radioButton_ImgType2.UseVisualStyleBackColor = true;
            radioButton_ImgType2.CheckedChanged += radioButton_ImgType2_CheckedChanged;
            // 
            // radioButton_ImgType1
            // 
            radioButton_ImgType1.AutoSize = true;
            radioButton_ImgType1.Location = new Point(165, 27);
            radioButton_ImgType1.Name = "radioButton_ImgType1";
            radioButton_ImgType1.Size = new Size(57, 21);
            radioButton_ImgType1.TabIndex = 2;
            radioButton_ImgType1.TabStop = true;
            radioButton_ImgType1.Text = "样式1";
            radioButton_ImgType1.UseVisualStyleBackColor = true;
            radioButton_ImgType1.CheckedChanged += radioButton_ImgType1_CheckedChanged;
            // 
            // checkBox_6Rows
            // 
            checkBox_6Rows.AutoSize = true;
            checkBox_6Rows.Location = new Point(95, 29);
            checkBox_6Rows.Name = "checkBox_6Rows";
            checkBox_6Rows.Size = new Size(46, 21);
            checkBox_6Rows.TabIndex = 1;
            checkBox_6Rows.Text = "6行";
            checkBox_6Rows.UseVisualStyleBackColor = true;
            // 
            // checkBox_7Rows
            // 
            checkBox_7Rows.AutoSize = true;
            checkBox_7Rows.Location = new Point(33, 29);
            checkBox_7Rows.Name = "checkBox_7Rows";
            checkBox_7Rows.Size = new Size(46, 21);
            checkBox_7Rows.TabIndex = 0;
            checkBox_7Rows.Text = "7行";
            checkBox_7Rows.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(uiButton_CancelBetData);
            groupBox2.Controls.Add(textBox_CancelIssue);
            groupBox2.Location = new Point(344, 80);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(314, 65);
            groupBox2.TabIndex = 4;
            groupBox2.TabStop = false;
            groupBox2.Text = "撤销期号答题 (默认最后一期)";
            // 
            // uiButton_CancelBetData
            // 
            uiButton_CancelBetData.Cursor = Cursors.Hand;
            uiButton_CancelBetData.FillColor = Color.FromArgb(230, 80, 80);
            uiButton_CancelBetData.FillColor2 = Color.FromArgb(230, 80, 80);
            uiButton_CancelBetData.FillHoverColor = Color.FromArgb(235, 115, 115);
            uiButton_CancelBetData.FillPressColor = Color.FromArgb(184, 64, 64);
            uiButton_CancelBetData.FillSelectedColor = Color.FromArgb(184, 64, 64);
            uiButton_CancelBetData.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_CancelBetData.LightColor = Color.FromArgb(253, 243, 243);
            uiButton_CancelBetData.Location = new Point(199, 21);
            uiButton_CancelBetData.MinimumSize = new Size(1, 1);
            uiButton_CancelBetData.Name = "uiButton_CancelBetData";
            uiButton_CancelBetData.RectColor = Color.FromArgb(230, 80, 80);
            uiButton_CancelBetData.RectHoverColor = Color.FromArgb(235, 115, 115);
            uiButton_CancelBetData.RectPressColor = Color.FromArgb(184, 64, 64);
            uiButton_CancelBetData.RectSelectedColor = Color.FromArgb(184, 64, 64);
            uiButton_CancelBetData.Size = new Size(100, 30);
            uiButton_CancelBetData.Style = Sunny.UI.UIStyle.Custom;
            uiButton_CancelBetData.TabIndex = 20;
            uiButton_CancelBetData.Text = "确定";
            uiButton_CancelBetData.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_CancelBetData.Click += uiButton_CancelBetData_Click;
            // 
            // textBox_CancelIssue
            // 
            textBox_CancelIssue.BorderStyle = BorderStyle.FixedSingle;
            textBox_CancelIssue.Location = new Point(18, 27);
            textBox_CancelIssue.Name = "textBox_CancelIssue";
            textBox_CancelIssue.Size = new Size(150, 23);
            textBox_CancelIssue.TabIndex = 0;
            textBox_CancelIssue.TextAlign = HorizontalAlignment.Center;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(uiButton_OneKeyRebate);
            groupBox1.Controls.Add(uiButton_SaveReturnCommissionPercent);
            groupBox1.Controls.Add(checkBox_ShowReturnCommissionDetail);
            groupBox1.Controls.Add(textBox_ReturnCommissionPercent);
            groupBox1.Location = new Point(13, 151);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(314, 65);
            groupBox1.TabIndex = 3;
            groupBox1.TabStop = false;
            groupBox1.Text = "回水比例";
            // 
            // uiButton_OneKeyRebate
            // 
            uiButton_OneKeyRebate.Cursor = Cursors.Hand;
            uiButton_OneKeyRebate.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_OneKeyRebate.Location = new Point(197, 36);
            uiButton_OneKeyRebate.MinimumSize = new Size(1, 1);
            uiButton_OneKeyRebate.Name = "uiButton_OneKeyRebate";
            uiButton_OneKeyRebate.Size = new Size(100, 23);
            uiButton_OneKeyRebate.TabIndex = 18;
            uiButton_OneKeyRebate.Text = "一键回水";
            uiButton_OneKeyRebate.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_OneKeyRebate.Click += uiButton_OneKeyRebate_Click;
            // 
            // uiButton_SaveReturnCommissionPercent
            // 
            uiButton_SaveReturnCommissionPercent.Cursor = Cursors.Hand;
            uiButton_SaveReturnCommissionPercent.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_SaveReturnCommissionPercent.Location = new Point(95, 25);
            uiButton_SaveReturnCommissionPercent.MinimumSize = new Size(1, 1);
            uiButton_SaveReturnCommissionPercent.Name = "uiButton_SaveReturnCommissionPercent";
            uiButton_SaveReturnCommissionPercent.Size = new Size(80, 30);
            uiButton_SaveReturnCommissionPercent.TabIndex = 17;
            uiButton_SaveReturnCommissionPercent.Text = "确定";
            uiButton_SaveReturnCommissionPercent.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_SaveReturnCommissionPercent.Click += uiButton_SaveReturnCommissionPercent_Click;
            // 
            // checkBox_ShowReturnCommissionDetail
            // 
            checkBox_ShowReturnCommissionDetail.AutoSize = true;
            checkBox_ShowReturnCommissionDetail.Location = new Point(198, 14);
            checkBox_ShowReturnCommissionDetail.Name = "checkBox_ShowReturnCommissionDetail";
            checkBox_ShowReturnCommissionDetail.Size = new Size(99, 21);
            checkBox_ShowReturnCommissionDetail.TabIndex = 2;
            checkBox_ShowReturnCommissionDetail.Text = "显示回水金额";
            checkBox_ShowReturnCommissionDetail.UseVisualStyleBackColor = true;
            checkBox_ShowReturnCommissionDetail.CheckedChanged += checkBox_ShowReturnCommissionDetail_CheckedChanged;
            // 
            // textBox_ReturnCommissionPercent
            // 
            textBox_ReturnCommissionPercent.BorderStyle = BorderStyle.FixedSingle;
            textBox_ReturnCommissionPercent.Location = new Point(18, 29);
            textBox_ReturnCommissionPercent.Name = "textBox_ReturnCommissionPercent";
            textBox_ReturnCommissionPercent.Size = new Size(71, 23);
            textBox_ReturnCommissionPercent.TabIndex = 0;
            textBox_ReturnCommissionPercent.TextAlign = HorizontalAlignment.Center;
            // 
            // tabPage_Log
            // 
            tabPage_Log.Controls.Add(dataGridView_Log);
            tabPage_Log.Location = new Point(4, 26);
            tabPage_Log.Name = "tabPage_Log";
            tabPage_Log.Padding = new Padding(3);
            tabPage_Log.Size = new Size(1275, 678);
            tabPage_Log.TabIndex = 9;
            tabPage_Log.Text = "▶日志";
            tabPage_Log.UseVisualStyleBackColor = true;
            // 
            // dataGridView_Log
            // 
            dataGridView_Log.AllowUserToAddRows = false;
            dataGridView_Log.AllowUserToDeleteRows = false;
            dataGridViewCellStyle37.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle37.BackColor = SystemColors.Control;
            dataGridViewCellStyle37.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle37.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle37.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle37.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle37.WrapMode = DataGridViewTriState.True;
            dataGridView_Log.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle37;
            dataGridView_Log.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView_Log.Columns.AddRange(new DataGridViewColumn[] { Log_Id, Log_Type, Log_Time, Log_Title, Log_Content });
            dataGridViewCellStyle38.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle38.BackColor = SystemColors.Window;
            dataGridViewCellStyle38.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle38.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle38.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle38.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle38.WrapMode = DataGridViewTriState.False;
            dataGridView_Log.DefaultCellStyle = dataGridViewCellStyle38;
            dataGridView_Log.Dock = DockStyle.Fill;
            dataGridView_Log.Location = new Point(3, 3);
            dataGridView_Log.Name = "dataGridView_Log";
            dataGridView_Log.ReadOnly = true;
            dataGridViewCellStyle39.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle39.BackColor = SystemColors.Control;
            dataGridViewCellStyle39.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle39.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle39.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle39.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle39.WrapMode = DataGridViewTriState.True;
            dataGridView_Log.RowHeadersDefaultCellStyle = dataGridViewCellStyle39;
            dataGridView_Log.Size = new Size(1269, 672);
            dataGridView_Log.TabIndex = 1;
            dataGridView_Log.VirtualMode = true;
            // 
            // Log_Id
            // 
            Log_Id.HeaderText = "序号";
            Log_Id.Name = "Log_Id";
            Log_Id.ReadOnly = true;
            Log_Id.Width = 60;
            // 
            // Log_Type
            // 
            Log_Type.HeaderText = "类型";
            Log_Type.Name = "Log_Type";
            Log_Type.ReadOnly = true;
            Log_Type.Width = 80;
            // 
            // Log_Time
            // 
            Log_Time.HeaderText = "时间";
            Log_Time.Name = "Log_Time";
            Log_Time.ReadOnly = true;
            Log_Time.Width = 130;
            // 
            // Log_Title
            // 
            Log_Title.HeaderText = "摘要";
            Log_Title.Name = "Log_Title";
            Log_Title.ReadOnly = true;
            Log_Title.Width = 200;
            // 
            // Log_Content
            // 
            Log_Content.HeaderText = "详情";
            Log_Content.Name = "Log_Content";
            Log_Content.ReadOnly = true;
            Log_Content.Width = 800;
            // 
            // contextMenuStrip_Menu
            // 
            contextMenuStrip_Menu.Name = "contextMenuStrip_Menu";
            contextMenuStrip_Menu.Size = new Size(61, 4);
            // 
            // notifyIcon_Pass
            // 
            notifyIcon_Pass.Text = "notifyIcon1";
            notifyIcon_Pass.Visible = true;
            // 
            // notifyIcon_NG
            // 
            notifyIcon_NG.Text = "notifyIcon1";
            notifyIcon_NG.Visible = true;
            // 
            // FormMain
            // 
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1283, 737);
            Controls.Add(tabControl_Main);
            Controls.Add(statusStrip_Foot);
            Icon = (Icon)resources.GetObject("$this.Icon");
            Name = "FormMain";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Robot";
            FormClosing += FormMain_FormClosing;
            Load += FormMain_Load;
            statusStrip_Foot.ResumeLayout(false);
            statusStrip_Foot.PerformLayout();
            tabControl_Main.ResumeLayout(false);
            tabPage_Home.ResumeLayout(false);
            tableLayoutPanel_Main.ResumeLayout(false);
            tableLayoutPanel_Body.ResumeLayout(false);
            tableLayoutPanel1.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_BetOrderDetail).EndInit();
            ((ISupportInitialize)dataGridView_SubMoney).EndInit();
            ((ISupportInitialize)dataGridView_AddMoney).EndInit();
            tableLayoutPanel_MemberAndBetOrder.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_MemberInfo).EndInit();
            panel8.ResumeLayout(false);
            panel9.ResumeLayout(false);
            panel10.ResumeLayout(false);
            tabPage_BetOrderReport.ResumeLayout(false);
            tableLayoutPanel2.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_BetOrderReport).EndInit();
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            tabPage_BetTotalReport.ResumeLayout(false);
            tableLayoutPanel3.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_HuiZongReport).EndInit();
            panel2.ResumeLayout(false);
            panel2.PerformLayout();
            tabPage_AddMoneyReport.ResumeLayout(false);
            tableLayoutPanel4.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_AddMoneyReport).EndInit();
            panel3.ResumeLayout(false);
            panel3.PerformLayout();
            tabPage_SubMoneyReport.ResumeLayout(false);
            tableLayoutPanel5.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_SubMoneyReport).EndInit();
            panel4.ResumeLayout(false);
            panel4.PerformLayout();
            tabPage_FinanceReport.ResumeLayout(false);
            tableLayoutPanel6.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_FinanceReport).EndInit();
            panel5.ResumeLayout(false);
            panel5.PerformLayout();
            tabPage_LaShou.ResumeLayout(false);
            tableLayoutPanel9.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_LaShou).EndInit();
            panel7.ResumeLayout(false);
            panel7.PerformLayout();
            tabPage_RecMsg.ResumeLayout(false);
            tableLayoutPanel7.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_RecMsg).EndInit();
            panel6.ResumeLayout(false);
            panel6.PerformLayout();
            tabPage_Setting.ResumeLayout(false);
            tableLayoutPanel8.ResumeLayout(false);
            panel_Setting.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_PlatformLog).EndInit();
            groupBox4.ResumeLayout(false);
            groupBox8.ResumeLayout(false);
            groupBox8.PerformLayout();
            groupBox6.ResumeLayout(false);
            groupBox6.PerformLayout();
            groupBox5.ResumeLayout(false);
            groupBox5.PerformLayout();
            groupBox3.ResumeLayout(false);
            groupBox3.PerformLayout();
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            tabPage_Log.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_Log).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private StatusStrip statusStrip_Foot;
        private System.Windows.Forms.TabControl tabControl_Main;
        private TabPage tabPage_Home;
        private TabPage tabPage_BetOrderReport;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_Main;
        private TabPage tabPage_BetTotalReport;
        private TabPage tabPage_AddMoneyReport;
        private TabPage tabPage_SubMoneyReport;
        private TabPage tabPage_FinanceReport;
        private TabPage tabPage_RecMsg;
        private TabPage tabPage_Setting;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_Body;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_MemberAndBetOrder;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.DataGridView dataGridView_SubMoney;
        private System.Windows.Forms.DataGridView dataGridView_AddMoney;
        private TableLayoutPanel tableLayoutPanel2;
        private TableLayoutPanel tableLayoutPanel3;
        private TableLayoutPanel tableLayoutPanel4;
        private TableLayoutPanel tableLayoutPanel5;
        private TableLayoutPanel tableLayoutPanel6;
        private TableLayoutPanel tableLayoutPanel7;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel8;
        private System.Windows.Forms.Panel panel_Setting;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ComboBox comboBox_WorkGroupId;
        private System.Windows.Forms.TextBox textBox_ReturnCommissionPercent;
        private System.Windows.Forms.GroupBox groupBox2;
        private TextBox textBox_CancelIssue;
        private System.Windows.Forms.GroupBox groupBox3;
        private CheckBox checkBox_6Rows;
        private CheckBox checkBox_7Rows;
        private System.Windows.Forms.GroupBox groupBox5;
        private CheckBox checkBox_SaveMemberBalance;
        private CheckBox checkBox_SaveMemberBaseInfo;
        private System.Windows.Forms.GroupBox groupBox6;
        private Panel panel1;
        private Label label3;
        private ComboBox comboBox_SelectIssueForBetOrder;
        private ComboBox comboBox_SelectMemberForBetOrder;
        private Label label4;
        private Panel panel2;
        private ComboBox comboBox_SelectIssueForHuiZong;
        private Label label6;
        private Panel panel3;
        private ComboBox comboBox_SelectObjForAddMoneyReport;
        private Label label7;
        private Panel panel4;
        private ComboBox comboBox_SelectObjForSubMoneyReport;
        private Label label8;
        private Panel panel5;
        private ComboBox comboBox_SelectObjForFinanceReport;
        private Label label5;
        private Panel panel6;
        private ComboBox comboBox_SelectMemberForShowRecMsg;
        private Label label9;
        private TabPage tabPage_Log;
        private DataGridView dataGridView_BetOrderReport;
        private DataGridView dataGridView_HuiZongReport;
        private DataGridView dataGridView_AddMoneyReport;
        private DataGridView dataGridView_SubMoneyReport;
        private DataGridView dataGridView_FinanceReport;
        private DataGridView dataGridView_RecMsg;
        private System.Windows.Forms.DataGridView dataGridView_Log;
        private ContextMenuStrip contextMenuStrip_Menu;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel_MemberInfo;
        private ToolStripStatusLabel toolStripStatusLabel_Lottery;
        private ToolStripStatusLabel toolStripStatusLabel_RobotInfo;
        private System.Windows.Forms.CheckBox checkBox_JiaRenAutoAddMoney;
        private System.Windows.Forms.CheckBox checkBox_AutoHuiShui;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox comboBox_CloseTime;
        private DataGridViewTextBoxColumn Log_Id;
        private DataGridViewTextBoxColumn Log_Type;
        private DataGridViewTextBoxColumn Log_Time;
        private DataGridViewTextBoxColumn Log_Title;
        private DataGridViewTextBoxColumn Log_Content;
        private NotifyIcon notifyIcon_Pass;
        private System.Windows.Forms.NotifyIcon notifyIcon_NG;
        private System.Windows.Forms.CheckBox checkBox_ShowReturnCommissionDetail;
        private GroupBox groupBox8;
        private RadioButton radioButton_ImgType1;
        private RadioButton radioButton_ImgType2;
        private TabPage tabPage_LaShou;
        private TableLayoutPanel tableLayoutPanel9;
        private DataGridView dataGridView_LaShou;
        private Panel panel7;
        private Sunny.UI.UIButton uiButton_CheckLaShou;
        private Sunny.UI.UIButton uiButton_JieSuanLaShou;
        private Label label_LaShouTips;
        private GroupBox groupBox4;
        private Sunny.UI.UIButton uiButton_Odds;
        private Sunny.UI.UIButton uiButton_Clear;
        private Sunny.UI.UIButton uiButton_CancelBetData;
        private Sunny.UI.UIButton uiButton_SaveReturnCommissionPercent;
        private Sunny.UI.UIButton uiButton_ShowTime;
        private Sunny.UI.UIButton uiButton_OneKeyRebate;
        private CheckBox checkBox_IsDuiChong;
        private DataGridView dataGridView_PlatformLog;
        private DataGridViewTextBoxColumn PlatformLog_Id;
        private DataGridViewTextBoxColumn PlatformLog_Time;
        private DataGridViewTextBoxColumn PlatformLog_Title;
        private DataGridViewTextBoxColumn PlatformLog_Content;
        private DataGridView dataGridView_BetOrderDetail;
        private DataGridView dataGridView_MemberInfo;
        private Panel panel8;
        private Panel panel9;
        private Panel panel10;
        private Button button_仿;
        private Button button_模;
        private Button button_版;
        private Button button_正;
        private Button button_卡奖时手动开奖或退单;
        private Button button_选择Q群;
        private Label label_总积分;
        private Label label_总人数;
        private Button button_撤销用户投注;
        private Button button_开奖历史;
        private Label label_番摊结果;
        private Label label10;
        private Label label_开奖号码;
        private Label label1;
        private Label label_收单状态;
        private Label label_开奖期数;
        private Label label_开奖期数标题;
        private Label label_封盘倒计时;
        private Label label_正在投注期数;
        private Label label_正在投注期数标题;
        private Button button_StopService;
        private Button button_StartService;
        private Button button_StopBet;
        private Button button_StartBet;
        private DataGridViewTextBoxColumn BetOrderDetail_Issue;
        private DataGridViewTextBoxColumn BetOrderDetail_RemarkName;
        private DataGridViewTextBoxColumn BetOrderDetail_Account;
        private DataGridViewTextBoxColumn BetOrderDetail_Content;
        private DataGridViewTextBoxColumn BetOrderDetail_Balance;
        private DataGridViewTextBoxColumn BetOrderDetail_BetStatus;
        private DataGridViewTextBoxColumn SubMoney_Id;
        private DataGridViewTextBoxColumn SubMoney_Account;
        private DataGridViewTextBoxColumn SubMoney_RemarkName;
        private DataGridViewTextBoxColumn SubMoney_Money;
        private DataGridViewButtonColumn SubMoney_Agree;
        private DataGridViewButtonColumn SubMoney_Refuse;
        private DataGridViewTextBoxColumn AddMoney_Id;
        private DataGridViewTextBoxColumn AddMoney_Account;
        private DataGridViewTextBoxColumn AddMoney_RemarkName;
        private DataGridViewTextBoxColumn AddMoney_Money;
        private DataGridViewButtonColumn AddMoney_Agree;
        private DataGridViewButtonColumn AddMoney_Refuse;
        private ToolStripStatusLabel toolStripStatusLabel_TodayWinTitle;
        private ToolStripStatusLabel toolStripStatusLabel_CurrentIssueBetTitle;
        private ToolStripStatusLabel toolStripStatusLabel_LastIssueWinTitle;
        private ToolStripStatusLabel toolStripStatusLabel1;
        private ToolStripStatusLabel toolStripStatusLabel2;
        private ToolStripStatusLabel toolStripStatusLabel3;
        private ToolStripStatusLabel toolStripStatusLabel_TodayWin;
        private ToolStripStatusLabel toolStripStatusLabel_CurrentIssueBet;
        private ToolStripStatusLabel toolStripStatusLabel_LastIssueWin;
    }
}
