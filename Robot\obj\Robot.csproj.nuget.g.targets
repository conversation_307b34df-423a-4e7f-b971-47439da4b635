﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)fody\6.8.2\build\Fody.targets" Condition="Exists('$(NuGetPackageRoot)fody\6.8.2\build\Fody.targets')" />
    <Import Project="$(NuGetPackageRoot)costura.fody\6.0.0\build\Costura.Fody.targets" Condition="Exists('$(NuGetPackageRoot)costura.fody\6.0.0\build\Costura.Fody.targets')" />
  </ImportGroup>
</Project>