﻿using AiHelper;
using FreeSql;
using Robot.ChatPlatform;
using Robot.Enum;
using Robot.Models;

namespace Robot.Helper;

public static class AddMoneyHelper
{
    /// <summary>
    /// 上分处理-同意
    /// </summary>
    /// <param name="am"></param>
    public static async Task AddMoneyHandlerAgree(AddMoney am)
    {
        try
        {
            // 获取会员信息
            Member member = await DbHelper.FSql.Select<Member>()
                .Where(t => t.Account.Equals(am.Account))
                .ToOneAsync();

            lock (DbHelper.DbLock)
            {
                // 使用事务操作数据库
                using DbContext dbContext = DbHelper.FSql.CreateDbContext();

                // 更新用户信息可用分值
                DbHelper.FSql.Update<Member>()
                    .Set(a => a.Balance, member.Balance + am.Money)
                    .Where(a => a.Account == member.Account)
                    .ExecuteAffrowsAsync();

                // 修改申请单状态
                DbHelper.FSql.Update<AddMoney>()
                    .Set(a => a.Status, EnumBalanceStatus.同意)
                    .Set(a => a.PreTime, DateTime.Now)
                    .Where(a => a.Id == am.Id)
                    .ExecuteAffrowsAsync();

                // 记录财务凭据
                Finance fn = new Finance
                {
                    Account = member.Account,
                    变动前 = member.Balance,
                    变动值 = am.Money,
                    变动后 = member.Balance + am.Money,
                    凭据 = "审核上分",
                    对应信息 = am.FromMsgId
                };
                DbHelper.FSql.Insert<Finance>()
                    .AppendData(fn)
                    .ExecuteIdentityAsync();

                // 提交保存操作
                dbContext.SaveChangesAsync();
            }

            // 发送群信息提醒
            decimal newBalance = member.Balance + am.Money;
            await ChatHelper.SendGroupMessage($@"上分{Ai.中括号左}{am.Money}{Ai.中括号右}成功" + "\r" + CommonHelper.GetFaceIdMoneyBag() + ":" + Math.Round(newBalance, 2), member.Account);
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "AddMoneyHandlerError", ex.ToString());
        }
    }

    /// <summary>
    /// 上分处理-拒绝
    /// </summary>
    /// <param name="am"></param>
    public static async Task AddMoneyHandlerRefuse(AddMoney am)
    {
        try
        {
            // 修改申请单状态
            await DbHelper.FSql.Update<AddMoney>()
                .Set(a => a.Status, EnumBalanceStatus.拒绝)
                .Set(a => a.PreTime, DateTime.Now)
                .Where(a => a.Id == am.Id)
                .ExecuteAffrowsAsync();

            // 获取会员信息
            Member member = await DbHelper.FSql.Select<Member>()
                .Where(t => t.Account.Equals(am.Account))
                .ToOneAsync();

            // 发送群信息提醒
            await ChatHelper.SendGroupMessage($@"查分{Ai.中括号左}{am.Money}{Ai.中括号右}失败" + "\r" + CommonHelper.GetFaceIdMoneyBag() + ":" + Math.Round(member.Balance, 2), member.Account);
        }
        catch (Exception ex)
        {
            await DbHelper.AddLogAsync(EnumLogType.机器人, "AddMoneyHandlerError", ex.ToString());
        }
    }
}